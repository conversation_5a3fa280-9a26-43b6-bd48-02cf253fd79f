import * as Tone from 'tone';

class SignalChainManager {
    private nodes: any[] = [];
    private context: AudioContext;
    private isPaused: boolean = false;

    constructor(audioContext: any) {
        this.context = audioContext;
    }

    // Add a node to the chain
    addNode(name: string, node: any, status: boolean = true): void {
        let single_node = {
            name: name,
            status: status,
            plug: node
        };
        this.nodes.push({...single_node});
    }

    // Connect nodes in the chain based on their status
    connectChain(): void {
        for (let i = 0; i < this.nodes.length - 1; i++) {
            let sourceNodeIndex = i;
            let destinationNodeIndex = i + 1;

            // Find the closest previous node with status true
            while (sourceNodeIndex >= 0 && !this.nodes[sourceNodeIndex].status) {
                sourceNodeIndex--;
            }

            // Find the closest upcoming node with status true
            while (destinationNodeIndex < this.nodes.length && !this.nodes[destinationNodeIndex].status) {
                destinationNodeIndex++;
            }

            // Connect to the closest nodes with status true
            if (sourceNodeIndex >= 0 && destinationNodeIndex < this.nodes.length) {
                Tone.connect(
                    this.nodes[sourceNodeIndex].plug.output || this.nodes[sourceNodeIndex].plug,
                    this.nodes[destinationNodeIndex].plug || this.nodes[destinationNodeIndex].plug.input
                );
                // (this.nodes[sourceNodeIndex].plug.output || this.nodes[sourceNodeIndex].plug).connect(
                //     this.nodes[destinationNodeIndex].plug || this.nodes[destinationNodeIndex].plug.input
                // );
            }
        }
    }

    // Disconnect nodes in the chain
    disconnectChain(): void {
        for (let i = 0; i < this.nodes.length - 1; i++) {
            Tone.disconnect(this.nodes[i].plug || this.nodes[i].plug.output || this.nodes[i].plug.input);
            // (this.nodes[i].plug || this.nodes[i].plug.output || this.nodes[i].plug.input).disconnect();
        }
    }

    // Disconnect and connect nodes in the chain
    reconnectChain(): void {
        this.disconnectChain();
        this.connectChain();
        this.connectToDestination();
    }

    // Dynamically attach a node by name and update the status
    attachNode(nodeName: string, applyChanges: boolean = true): void {
        const nodeIndex = this.nodes.findIndex(node => node.name === nodeName);

        if (nodeIndex !== -1 && nodeIndex <= this.nodes.length) {
            if(!this.nodes[nodeIndex].status){
                this.nodes[nodeIndex].status = true;
                if(applyChanges){
                    this.reconnectChain();
                }
            }
        }
    }

    // Dynamically detach a node by name and update the status
    detachNode(nodeName: string, applyChanges: boolean = true): void {
        const nodeIndex = this.nodes.findIndex(node => node.name === nodeName);
        if (nodeIndex !== -1 && nodeIndex <= this.nodes.length) {
            if(this.nodes[nodeIndex].status){
                this.nodes[nodeIndex].status = false;
                if(applyChanges){
                    this.reconnectChain();
                }
            }
        }
    }

    // Apply changes on attach and detach
    applyChanges(): void {
        this.reconnectChain();
    }

    // order nodes
    orderNodes(nodeOrder: string[], reConnect: boolean = false): void {
        // Validate if all provided node names exist in the current nodes
        const validNodeOrder = nodeOrder.filter(name => this.nodes.some(node => node.name === name));
    
        // Create a new array to hold the ordered nodes
        const orderedNodes: any[] = [];
    
        // Add nodes in the order specified by the nodeOrder array
        validNodeOrder.forEach(name => {
            const node = this.nodes.find(n => n.name === name);
            if (node) {
                orderedNodes.push({...node});
            }
        });
    
        // Add remaining nodes that were not specified in nodeOrder
        this.nodes.forEach(node => {
            if (!validNodeOrder.includes(node.name)) {
                orderedNodes.push({...node});
            }
        });
    
        // Update the nodes array with the ordered nodes
        this.nodes = orderedNodes;
    
        if(reConnect){
            // Reconnect the chain based on the new order
            this.reconnectChain();
        }
    }    

    // Connect the last active node in the chain to the destination
    connectToDestination(): void {
        let lastIndex = this.nodes.length - 1;

        // Find the closest previous node with status true
        while (lastIndex >= 0 && !this.nodes[lastIndex].status) {
            lastIndex--;
        }

        if (lastIndex >= 0) {
            Tone.connect(this.nodes[lastIndex].plug.output || this.nodes[lastIndex].plug, this.context.destination);
            // (this.nodes[lastIndex].plug.output || this.nodes[lastIndex].plug).connect(this.context.destination);
        }
    }
    // Connect the last active node in the chain to the destination
    toDestination(node: any): void {
        Tone.connect(node.output || node, this.context.destination);
    }

    // export latest node
    export(): any {
        const lastIndex = this.nodes.length - 1;

        if (lastIndex >= 0) {
            return this.nodes[lastIndex].plug;
        }

        return false;
    }

    // Pause the audio context
    pause(): void {
        if (!this.isPaused) {
            this.context.suspend().then(() => {
                this.isPaused = true;
            });
        }
    }

    // Resume the audio context
    resume(): void {
        if (this.isPaused) {
            this.context.resume().then(() => {
                this.isPaused = false;
            });
        }
    }
}

export default SignalChainManager;