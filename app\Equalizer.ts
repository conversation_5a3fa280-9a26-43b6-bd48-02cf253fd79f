import <PERSON><PERSON> from "tunajs";
import * as To<PERSON> from 'tone';
import Signal<PERSON>hainManager from "./SignalChainManager";
import bighall_impulse from 'url:app/audio/big-hall.wav';
import UserProfile from "./UserProfile";

const Equalizer: Record<string, any> = {
    stream_object: {},
    disconnected_settings: {},
    default_settings: UserProfile,
    configureSettings: async function (settings: any) {
        // Clone the default settings and merge with the provided settings, favoring the provided settings.
        if (typeof settings == 'object' && settings != undefined) {
            return { ...Equalizer.default_settings, ...settings };
        } else {
            return { ...Equalizer.default_settings, ...{} };
        }
    },

    tabSettings: async function (tabId: string) {
        return Equalizer.stream_object[tabId].settings;
    },

    init: function (tabId: string, streamId: any, initConfig: any = {}) {
        let audioContext: AudioContext | null = null;
        let tabStream: MediaStream | null = null;
        let tabMediaSource: MediaStreamAudioSourceNode | null = null;

        /* read: https://developer.mozilla.org/en-US/docs/Web/API/AudioContext/AudioContext#latencyhint
        low -- > high latency
        interactive / balanced / playback
        */

        // AudioContext
        audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
            latencyHint: 'playback'
        });

        // tabStream
        if (!tabStream || tabStream == null) {
            tabStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    mandatory: {
                        chromeMediaSource: 'tab',
                        chromeMediaSourceId: streamId,
                    }
                },
                video: false,
            });
        }

        // MediaStreamSource
        tabMediaSource = audioContext.createMediaStreamSource(tabStream);

        // exec
        Equalizer.start(tabId, audioContext, tabStream, tabMediaSource, initConfig);
    },

    updateEQBand: function (tabId: string, bandName: string, x: any, y: any) {
        const minFreq = 20;
        const maxFreq = 20000;
        const minGain = -30;
        const maxGain = 30;

        // Convert x to log-scale frequency (20 Hz to 20 kHz)
        const frequency = minFreq * Math.pow(maxFreq / minFreq, x);
        const gain = minGain + (1 - y) * (maxGain - minGain); // Inverted Y

        const band = Equalizer.stream_object[tabId][bandName];
        const now = Equalizer.stream_object[tabId].audioContext.currentTime;

        band.frequency.setValueAtTime(frequency, now);
        band.gain.setValueAtTime(gain, now);

        // dev : updateBand(tabId, 'fiveHundred', x, y);
    },

    setup: async function (tabId: string, audioContext: AudioContext, mediaStream: MediaStream, mediaSource: MediaStreamAudioSourceNode, tabSettings: any = {}, reload: boolean = false) {

        if (!reload) {
            // info
            Equalizer.stream_object[tabId].id = tabId;

            // media
            Equalizer.stream_object[tabId].audioContext = audioContext;
            Equalizer.stream_object[tabId].mediaStream = mediaStream;
            Equalizer.stream_object[tabId].audioSource = mediaSource;
            Equalizer.stream_object[tabId].baseGain = Equalizer.stream_object[tabId].audioContext.createGain();
            Equalizer.stream_object[tabId].filteredGain = Equalizer.stream_object[tabId].audioContext.createGain();;

            // settings
            Equalizer.stream_object[tabId].settings = await Equalizer.configureSettings(
                (Equalizer.disconnected_settings[tabId] != undefined && Equalizer.disconnected_settings[tabId] != null) ?
                    Equalizer.disconnected_settings[tabId] :
                    tabSettings
            );

            // cleanup backup
            Equalizer.disconnected_settings[tabId] = null;

            Equalizer.stream_object[tabId].settings.connected = true;

            Equalizer.stream_object[tabId].manager = new SignalChainManager(Equalizer.stream_object[tabId].audioContext);
        }

        if (Equalizer.stream_object[tabId]?.settings) {
            Equalizer.stream_object[tabId].settings.connected = true;
        }

        if (!reload) {
            // tunajs
            Equalizer.stream_object[tabId].tunajs = new Tuna(Equalizer.stream_object[tabId].audioContext);
            // tonejs
            Tone.setContext(Equalizer.stream_object[tabId].audioContext);
            Equalizer.stream_object[tabId].tonejs = Tone.getContext();
        }

        if (!reload) {

            Equalizer.stream_object[tabId].panSplitter = Equalizer.stream_object[tabId].audioContext.createChannelSplitter(2);
            Equalizer.stream_object[tabId].leftGain = Equalizer.stream_object[tabId].audioContext.createGain();
            Equalizer.stream_object[tabId].rightGain = Equalizer.stream_object[tabId].audioContext.createGain();
            Equalizer.stream_object[tabId].panMerger = Equalizer.stream_object[tabId].audioContext.createChannelMerger(2);
            Equalizer.stream_object[tabId].monoSplitter = Equalizer.stream_object[tabId].audioContext.createChannelSplitter(2);
            Equalizer.stream_object[tabId].monoGain = Equalizer.stream_object[tabId].audioContext.createGain();
            Equalizer.stream_object[tabId].monoMerger = Equalizer.stream_object[tabId].audioContext.createChannelMerger(2);

            Equalizer.stream_object[tabId].bands = {
                band1: Equalizer.stream_object[tabId].audioContext.createBiquadFilter(),
                band2: Equalizer.stream_object[tabId].audioContext.createBiquadFilter(),
                band3: Equalizer.stream_object[tabId].audioContext.createBiquadFilter(),
                band4: Equalizer.stream_object[tabId].audioContext.createBiquadFilter(),
                band5: Equalizer.stream_object[tabId].audioContext.createBiquadFilter(),
                band6: Equalizer.stream_object[tabId].audioContext.createBiquadFilter(),
                band7: Equalizer.stream_object[tabId].audioContext.createBiquadFilter(),
                band8: Equalizer.stream_object[tabId].audioContext.createBiquadFilter(),
                band9: Equalizer.stream_object[tabId].audioContext.createBiquadFilter(),
                band10: Equalizer.stream_object[tabId].audioContext.createBiquadFilter(),
                band11: Equalizer.stream_object[tabId].audioContext.createBiquadFilter()
            };

            const freqs = [
                32,     // Sub-bass (low shelf)
                64,
                125,
                250,
                500,
                1000,
                2000,
                4000,
                8000,
                16000,  // Highs (maybe high shelf)
                20480   // Optional extra sparkle/air
            ];

            const bandTypes = [
                'lowshelf',  // Band 1: Low shelf for sub-bass
                'peaking',   // Band 2: Peaking filter for 64Hz
                'peaking',   // Band 3: Peaking filter for 125Hz
                'peaking',   // Band 4: Peaking filter for 250Hz
                'peaking',   // Band 5: Peaking filter for 500Hz
                'peaking',   // Band 6: Peaking filter for 1000Hz
                'peaking',   // Band 7: Peaking filter for 2000Hz
                'peaking',   // Band 8: Peaking filter for 4000Hz
                'peaking',   // Band 9: Peaking filter for 8000Hz
                'highshelf', // Band 10: High shelf for 16000Hz
                'peaking'    // Band 11: Peaking filter for 20480Hz
            ];

            // Set default values for each band
            for (let i = 0; i < freqs.length; i++) {
                const bandKey = `band${i + 1}`;
                const band = Equalizer.stream_object[tabId].bands[bandKey];

                band.type = bandTypes[i];  // Set filter type for each band
                band.frequency.setValueAtTime(freqs[i], Equalizer.stream_object[tabId].audioContext.currentTime);  // Set frequency from `freqs` array
                band.gain.setValueAtTime(0, Equalizer.stream_object[tabId].audioContext.currentTime); // Default gain (0 dB)
                band.Q.setValueAtTime(1, Equalizer.stream_object[tabId].audioContext.currentTime);  // Default Q (1)
            }

            Equalizer.stream_object[tabId].twenty = Equalizer.stream_object[tabId].audioContext.createBiquadFilter();
            Equalizer.stream_object[tabId].fifty = Equalizer.stream_object[tabId].audioContext.createBiquadFilter();
            Equalizer.stream_object[tabId].oneHundred = Equalizer.stream_object[tabId].audioContext.createBiquadFilter();
            Equalizer.stream_object[tabId].twoHundred = Equalizer.stream_object[tabId].audioContext.createBiquadFilter();
            Equalizer.stream_object[tabId].fiveHundred = Equalizer.stream_object[tabId].audioContext.createBiquadFilter();
            Equalizer.stream_object[tabId].oneThousand = Equalizer.stream_object[tabId].audioContext.createBiquadFilter();
            Equalizer.stream_object[tabId].twoThousand = Equalizer.stream_object[tabId].audioContext.createBiquadFilter();
            Equalizer.stream_object[tabId].fiveThousand = Equalizer.stream_object[tabId].audioContext.createBiquadFilter();
            Equalizer.stream_object[tabId].tenThousand = Equalizer.stream_object[tabId].audioContext.createBiquadFilter();
            Equalizer.stream_object[tabId].twentyThousand = Equalizer.stream_object[tabId].audioContext.createBiquadFilter();

            Equalizer.stream_object[tabId].twenty.type = 'lowshelf';
            Equalizer.stream_object[tabId].twenty.frequency.setValueAtTime(32, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].twenty.gain.setValueAtTime(Number(Equalizer.stream_object[tabId].settings.eq[0]), Equalizer.stream_object[tabId].audioContext.currentTime);

            Equalizer.stream_object[tabId].fifty.type = 'peaking';
            Equalizer.stream_object[tabId].fifty.frequency.setValueAtTime(64, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].fifty.Q.setValueAtTime(5, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].fifty.gain.setValueAtTime(Number(Equalizer.stream_object[tabId].settings.eq[1]), Equalizer.stream_object[tabId].audioContext.currentTime);

            Equalizer.stream_object[tabId].oneHundred.type = 'peaking';
            Equalizer.stream_object[tabId].oneHundred.frequency.setValueAtTime(125, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].oneHundred.Q.setValueAtTime(5, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].oneHundred.gain.setValueAtTime(Number(Equalizer.stream_object[tabId].settings.eq[2]), Equalizer.stream_object[tabId].audioContext.currentTime);

            Equalizer.stream_object[tabId].twoHundred.type = 'peaking';
            Equalizer.stream_object[tabId].twoHundred.frequency.setValueAtTime(250, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].twoHundred.Q.setValueAtTime(5, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].twoHundred.gain.setValueAtTime(Number(Equalizer.stream_object[tabId].settings.eq[3]), Equalizer.stream_object[tabId].audioContext.currentTime);

            Equalizer.stream_object[tabId].fiveHundred.type = 'peaking';
            Equalizer.stream_object[tabId].fiveHundred.frequency.setValueAtTime(500, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].fiveHundred.Q.setValueAtTime(5, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].fiveHundred.gain.setValueAtTime(Number(Equalizer.stream_object[tabId].settings.eq[4]), Equalizer.stream_object[tabId].audioContext.currentTime);

            Equalizer.stream_object[tabId].oneThousand.type = 'peaking';
            Equalizer.stream_object[tabId].oneThousand.frequency.setValueAtTime(1000, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].oneThousand.Q.setValueAtTime(5, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].oneThousand.gain.setValueAtTime(Number(Equalizer.stream_object[tabId].settings.eq[5]), Equalizer.stream_object[tabId].audioContext.currentTime);

            Equalizer.stream_object[tabId].twoThousand.type = 'peaking';
            Equalizer.stream_object[tabId].twoThousand.frequency.setValueAtTime(2000, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].twoThousand.Q.setValueAtTime(5, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].twoThousand.gain.setValueAtTime(Number(Equalizer.stream_object[tabId].settings.eq[6]), Equalizer.stream_object[tabId].audioContext.currentTime);

            Equalizer.stream_object[tabId].fiveThousand.type = 'peaking';
            Equalizer.stream_object[tabId].fiveThousand.frequency.setValueAtTime(4000, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].fiveThousand.Q.setValueAtTime(5, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].fiveThousand.gain.setValueAtTime(Number(Equalizer.stream_object[tabId].settings.eq[7]), Equalizer.stream_object[tabId].audioContext.currentTime);

            Equalizer.stream_object[tabId].tenThousand.type = 'peaking';
            Equalizer.stream_object[tabId].tenThousand.frequency.setValueAtTime(8000, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].tenThousand.Q.setValueAtTime(5, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].tenThousand.gain.setValueAtTime(Number(Equalizer.stream_object[tabId].settings.eq[8]), Equalizer.stream_object[tabId].audioContext.currentTime);

            Equalizer.stream_object[tabId].twentyThousand.type = 'highshelf';
            Equalizer.stream_object[tabId].twentyThousand.frequency.setValueAtTime(16000, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].twentyThousand.gain.setValueAtTime(Number(Equalizer.stream_object[tabId].settings.eq[9]), Equalizer.stream_object[tabId].audioContext.currentTime);

            Equalizer.stream_object[tabId].filteredGain.gain.setValueAtTime(Equalizer.stream_object[tabId].settings.volume, Equalizer.stream_object[tabId].audioContext.currentTime);
            Equalizer.stream_object[tabId].monoGain.gain.setValueAtTime(0.6, Equalizer.stream_object[tabId].audioContext.currentTime);

            Equalizer.stream_object[tabId].audioSource.connect(Equalizer.stream_object[tabId].baseGain);
            Equalizer.stream_object[tabId].baseGain.connect(Equalizer.stream_object[tabId].panSplitter);
            Equalizer.stream_object[tabId].panSplitter.connect(Equalizer.stream_object[tabId].leftGain, 0);
            Equalizer.stream_object[tabId].panSplitter.connect(Equalizer.stream_object[tabId].rightGain, 1);
            Equalizer.stream_object[tabId].leftGain.connect(Equalizer.stream_object[tabId].panMerger, 0, 0);
            Equalizer.stream_object[tabId].rightGain.connect(Equalizer.stream_object[tabId].panMerger, 0, 1);
        }

        if (Equalizer.stream_object[tabId].settings.mono) {
            Equalizer.adjustAudioOutputMode(tabId, true);
        } else {
            Equalizer.adjustAudioOutputMode(tabId, false);
        }

        // monoMerger
        if (!reload && Equalizer.stream_object[tabId].monoMerger) {
            Equalizer.stream_object[tabId].manager.addNode('mono_merger', Equalizer.stream_object[tabId].monoMerger);
        }

        // Tonejs Chorus
        if (Equalizer.stream_object[tabId].settings.chorus.status) {
            if (!Equalizer.stream_object[tabId].chorus) {
                Equalizer.stream_object[tabId].chorus = new Tone.Chorus({
                    frequency: Equalizer.stream_object[tabId].settings.chorus.config.frequency, //1.5
                    delayTime: Equalizer.stream_object[tabId].settings.chorus.config.delayTime, //3.5,
                    depth: Equalizer.stream_object[tabId].settings.chorus.config.depth, //0.7,
                    spread: Equalizer.stream_object[tabId].settings.chorus.config.spread, //180,
                    wet: Equalizer.stream_object[tabId].settings.chorus.config.wet, //0.5,
                    feedback: 0,
                });
                Equalizer.stream_object[tabId].manager.addNode('tone_chorus', Equalizer.stream_object[tabId].chorus);
            } else {
                Equalizer.stream_object[tabId].manager.attachNode('tone_chorus', false);
            }
        } else if (Equalizer.stream_object[tabId].chorus) {
            Equalizer.stream_object[tabId].manager.detachNode('tone_chorus', false);
        }

        //extraEffects
        // .. 3d panner and other stuff
        if (Equalizer.stream_object[tabId].settings.spatial_audio.status) {
            if (!Equalizer.stream_object[tabId].spatial_audio) {
                Equalizer.stream_object[tabId].spatial_audio_interval = null;
                Equalizer.stream_object[tabId].spatial_audio = new Tone.Panner3D({
                    panningModel: "HRTF",
                    positionX: 0,
                    positionY: 0,
                    positionZ: 0
                });
                Equalizer.spatialAudio(Equalizer.stream_object[tabId]);
                Equalizer.stream_object[tabId].manager.addNode('spatial_audio', Equalizer.stream_object[tabId].spatial_audio);
            } else {
                Equalizer.stream_object[tabId].manager.attachNode('spatial_audio', false);
            }
        } else if (Equalizer.stream_object[tabId].spatial_audio) {
            Equalizer.stream_object[tabId].manager.detachNode('spatial_audio', false);
        }

        // Tonejs Reverb
        if (Equalizer.stream_object[tabId].settings.tone_reverb.status) {
            if (!Equalizer.stream_object[tabId].tone_reverb) {
                Equalizer.stream_object[tabId].tone_reverb = new Tone.Reverb({
                    wet: Equalizer.stream_object[tabId].settings.tone_reverb.config.wet,
                    decay: Equalizer.stream_object[tabId].settings.tone_reverb.config.decay,
                    preDelay: Equalizer.stream_object[tabId].settings.tone_reverb.config.preDelay
                });
                Equalizer.stream_object[tabId].manager.addNode('tone_reverb', Equalizer.stream_object[tabId].tone_reverb);
            } else {
                Equalizer.stream_object[tabId].manager.attachNode('tone_reverb', false);
            }
        } else if (Equalizer.stream_object[tabId].tone_reverb) {
            Equalizer.stream_object[tabId].manager.detachNode('tone_reverb', false);
        }

        // Tuna reverb
        if (Equalizer.stream_object[tabId].settings.tuna_reverb.status) {
            if (!Equalizer.stream_object[tabId].tuna_reverb) {
                Equalizer.stream_object[tabId].tuna_reverb = new Equalizer.stream_object[tabId].tunajs.Convolver({
                    highCut: Equalizer.stream_object[tabId].settings.tuna_reverb.config.highCut,
                    lowCut: Equalizer.stream_object[tabId].settings.tuna_reverb.config.lowCut,
                    dryLevel: Equalizer.stream_object[tabId].settings.tuna_reverb.config.dryLevel,
                    wetLevel: Equalizer.stream_object[tabId].settings.tuna_reverb.config.wetLevel,
                    level: Equalizer.stream_object[tabId].settings.tuna_reverb.config.level,
                    bypass: 0,
                    impulse: bighall_impulse
                });
                Equalizer.stream_object[tabId].manager.addNode('tuna_reverb', Equalizer.stream_object[tabId].tuna_reverb);
            } else {
                Equalizer.stream_object[tabId].manager.attachNode('tuna_reverb', false);
            }
        } else if (Equalizer.stream_object[tabId].tuna_reverb) {
            Equalizer.stream_object[tabId].manager.detachNode('tuna_reverb', false);
        }

        // Eq
        if (!reload) {
            Equalizer.stream_object[tabId].manager.addNode('eq_twenty', Equalizer.stream_object[tabId].twenty);
            Equalizer.stream_object[tabId].manager.addNode('eq_fifty', Equalizer.stream_object[tabId].fifty);
            Equalizer.stream_object[tabId].manager.addNode('eq_oneHundred', Equalizer.stream_object[tabId].oneHundred);
            Equalizer.stream_object[tabId].manager.addNode('eq_twoHundred', Equalizer.stream_object[tabId].twoHundred);
            Equalizer.stream_object[tabId].manager.addNode('eq_fiveHundred', Equalizer.stream_object[tabId].fiveHundred);
            Equalizer.stream_object[tabId].manager.addNode('eq_oneThousand', Equalizer.stream_object[tabId].oneThousand);
            Equalizer.stream_object[tabId].manager.addNode('eq_twoThousand', Equalizer.stream_object[tabId].twoThousand);
            Equalizer.stream_object[tabId].manager.addNode('eq_fiveThousand', Equalizer.stream_object[tabId].fiveThousand);
            Equalizer.stream_object[tabId].manager.addNode('eq_tenThousand', Equalizer.stream_object[tabId].tenThousand);
            Equalizer.stream_object[tabId].manager.addNode('eq_twentyThousand', Equalizer.stream_object[tabId].twentyThousand);
        }

        // Tuna Compressor
        if (Equalizer.stream_object[tabId].settings.compressor.status) {
            if (!Equalizer.stream_object[tabId].compressor) {

                Equalizer.stream_object[tabId].compressor = new Equalizer.stream_object[tabId].tunajs.Compressor({
                    bypass: 0,
                    automakeup: false,
                    threshold: Equalizer.stream_object[tabId].settings.compressor.config.threshold,
                    attack: Equalizer.stream_object[tabId].settings.compressor.config.attack,
                    release: Equalizer.stream_object[tabId].settings.compressor.config.release,
                    ratio: Equalizer.stream_object[tabId].settings.compressor.config.ratio,
                    knee: Equalizer.stream_object[tabId].settings.compressor.config.knee,
                });
                Equalizer.stream_object[tabId].compressor.automate('threshold', parseFloat(Equalizer.stream_object[tabId].settings.compressor.config.threshold));
                Equalizer.stream_object[tabId].compressor.automate('release', parseFloat(Equalizer.stream_object[tabId].settings.compressor.config.release));
                Equalizer.stream_object[tabId].compressor.automate("makeupGain", parseFloat(Equalizer.stream_object[tabId].settings.compressor.config.makeupGain))
                Equalizer.stream_object[tabId].compressor.automate('attack', parseFloat(Equalizer.stream_object[tabId].settings.compressor.config.attack));
                Equalizer.stream_object[tabId].compressor.automate('ratio', parseFloat(Equalizer.stream_object[tabId].settings.compressor.config.ratio));
                Equalizer.stream_object[tabId].compressor.automate('knee', parseFloat(Equalizer.stream_object[tabId].settings.compressor.config.knee));
                Equalizer.stream_object[tabId].manager.addNode('tuna_compressor', Equalizer.stream_object[tabId].compressor);
            } else {
                Equalizer.stream_object[tabId].manager.attachNode('tuna_compressor', false);
                Equalizer.stream_object[tabId].compressor.automate('release', parseFloat(Equalizer.stream_object[tabId].settings.compressor.config.release));
            }
        } else if (Equalizer.stream_object[tabId].compressor) {
            Equalizer.stream_object[tabId].manager.detachNode('tuna_compressor', false);
        }

        // filteredGain
        Equalizer.stream_object[tabId].filteredGain.gain.value = Equalizer.stream_object[tabId].settings.volume;
        Equalizer.stream_object[tabId].baseGain.gain.value = Equalizer.stream_object[tabId].settings.base_volume;
        if (!reload) {
            Equalizer.stream_object[tabId].manager.addNode('filteredGain', Equalizer.stream_object[tabId].filteredGain);
        }

        // fix nodes order
        Equalizer.stream_object[tabId].manager.orderNodes([
            'mono_merger',
            'tone_chorus',
            'spatial_audio',
            'tone_reverb',
            'tuna_reverb',
            'eq_twenty',
            'eq_fifty',
            'eq_oneHundred',
            'eq_twoHundred',
            'eq_fiveHundred',
            'eq_oneThousand',
            'eq_twoThousand',
            'eq_fiveThousand',
            'eq_tenThousand',
            'eq_twentyThousand',
            'tuna_compressor',
            'filteredGain',
        ]);

        if (!reload) {
            // chain and connect
            Equalizer.stream_object[tabId].manager.connectChain();
            Equalizer.stream_object[tabId].manager.connectToDestination();
        } else {
            // apply changes
            Equalizer.stream_object[tabId].manager.applyChanges();
        }

        // Noise
        if (Equalizer.stream_object[tabId].settings.noise.status) {
            if (!Equalizer.stream_object[tabId].noise) {
                Equalizer.stream_object[tabId].noise = new Tone.Noise({
                    mute: false,
                    volume: Equalizer.stream_object[tabId].settings.noise.config.volume,
                    fadeIn: 1,
                    fadeOut: 1,
                    playbackRate: 1,
                    type: Equalizer.stream_object[tabId].settings.noise.config.type
                });
                Equalizer.stream_object[tabId].manager.toDestination(Equalizer.stream_object[tabId].noise);
            }
            Equalizer.stream_object[tabId].noise.start();
        } else if (Equalizer.stream_object[tabId].noise) {
            Equalizer.stream_object[tabId].noise.stop();
        }

        return Equalizer.stream_object[tabId];
    },

    adjustAudioOutputMode: async function (tabId: string, status: boolean) {
        if (Equalizer.stream_object[tabId].panMerger.context.__connectified__ == 1) {
            Equalizer.stream_object[tabId].panMerger.disconnect();
        }
        if (Equalizer.stream_object[tabId].monoSplitter.context.__connectified__ == 1) {
            Equalizer.stream_object[tabId].monoSplitter.disconnect();
        }
        if (Equalizer.stream_object[tabId].monoGain.context.__connectified__ == 1) {
            Equalizer.stream_object[tabId].monoGain.disconnect();
        }
        if (status === true) {
            Equalizer.stream_object[tabId].panMerger.connect(Equalizer.stream_object[tabId].monoGain);
            Equalizer.stream_object[tabId].monoGain.connect(Equalizer.stream_object[tabId].monoSplitter);
            Equalizer.stream_object[tabId].monoSplitter.connect(Equalizer.stream_object[tabId].monoMerger, 0, 1);
            Equalizer.stream_object[tabId].monoSplitter.connect(Equalizer.stream_object[tabId].monoMerger, 0, 0);
            Equalizer.stream_object[tabId].monoSplitter.connect(Equalizer.stream_object[tabId].monoMerger, 1, 0);
        } else {
            Equalizer.stream_object[tabId].panMerger.connect(Equalizer.stream_object[tabId].monoSplitter);
            Equalizer.stream_object[tabId].monoSplitter.connect(Equalizer.stream_object[tabId].monoMerger, 0, 0);
        }

        Equalizer.stream_object[tabId].monoSplitter.connect(Equalizer.stream_object[tabId].monoMerger, 1, 1);
    },

    start: async function (tabId: string, audioContext: AudioContext, mediaStream: MediaStream, mediaSource: MediaStreamAudioSourceNode, tabSettings: any = {}) {
        Equalizer.stream_object[tabId] = {};
        return Equalizer.setup(tabId, audioContext, mediaStream, mediaSource, tabSettings, false);
    },

    cleanUpToneJS: async function (context, effects = []) {

        effects.forEach((effect) => {
            if (effect) {
                effect.dispose();
            }
        });

        // Close the audio context
        if (context) {
            context.dispose();
            //context.close().then(() => {}).catch(() => {});
        }
    },

    stop: async function (tabId: string, callback: any = function () { }) {

        // stop bg noise
        Equalizer.stopNoise(tabId);

        if (Equalizer.stream_object[tabId]?.settings) {
            Equalizer.stream_object[tabId].settings.connected = false;
            Equalizer.disconnected_settings[tabId] = { ...Equalizer.stream_object[tabId].settings };
        }

        if (Equalizer.stream_object[tabId]?.spatial_audio_interval) {
            clearInterval(Equalizer.stream_object[tabId].spatial_audio_interval);
        }

        Equalizer.cleanUpToneJS(Equalizer.stream_object[tabId]?.tonejs, [
            Equalizer.stream_object[tabId]?.tone_reverb,
            Equalizer.stream_object[tabId]?.chorus,
            Equalizer.stream_object[tabId]?.noise,
            Equalizer.stream_object[tabId]?.spatial_audio
        ]);

        if (Equalizer.stream_object[tabId] && Equalizer.stream_object[tabId].mediaStream) {
            await Promise.all(
                Equalizer.stream_object[tabId].mediaStream.getTracks().map((t) => t.stop())
            );
        }

        Equalizer.stream_object[tabId] = {};
        delete Equalizer.stream_object[tabId];

        callback(tabId);
    },

    destroy: async function (tabId: string, callback: any = function () { }) {

        // stop bg noise
        Equalizer.stopNoise(tabId);

        if (Equalizer.stream_object[tabId]?.spatial_audio_interval) {
            clearInterval(Equalizer.stream_object[tabId].spatial_audio_interval);
        }

        Equalizer.cleanUpToneJS(Equalizer.stream_object[tabId]?.tonejs, [
            Equalizer.stream_object[tabId]?.tone_reverb,
            Equalizer.stream_object[tabId]?.chorus,
            Equalizer.stream_object[tabId]?.noise,
            Equalizer.stream_object[tabId]?.spatial_audio
        ]);

        Equalizer.disconnected_settings[tabId] = null;
        Equalizer.stream_object[tabId] = {};

        delete Equalizer.stream_object[tabId];
        delete Equalizer.disconnected_settings[tabId];

        callback(tabId);
    },

    attach: async function (tabId) {
        return new Promise((resolve, reject) => {
            try {
                Equalizer.stream_object[tabId].settings.connected = true;
                // Your existing logic from the attach function
                Tone.disconnect(Equalizer.stream_object[tabId].audioSource);
                Equalizer.stream_object[tabId].manager.toDestination(Equalizer.stream_object[tabId].filteredGain);
                resolve(Equalizer.stream_object[tabId]);
            } catch (error) {
                reject(error);
            }
        });
    },

    detach: async function (tabId) {
        return new Promise((resolve, reject) => {
            try {
                Equalizer.stream_object[tabId].settings.connected = false;
                // Your existing logic from the detach function
                Tone.disconnect(Equalizer.stream_object[tabId].filteredGain);
                Equalizer.stream_object[tabId].manager.toDestination(Equalizer.stream_object[tabId].audioSource);
                resolve(Equalizer.stream_object[tabId]);
                if (Equalizer.stream_object[tabId].noise) {
                    Equalizer.stream_object[tabId].noise.stop();
                }
            } catch (error) {
                reject(error);
            }
        });
    },

    reload: async function (tabId: any, hardStop: boolean = false) {
        return new Promise((resolve, reject) => {
            try {
                if (Equalizer.stream_object[tabId]) {
                    Equalizer.setup(
                        tabId,
                        Equalizer.stream_object[tabId].audioContext,
                        Equalizer.stream_object[tabId].mediaStream,
                        Equalizer.stream_object[tabId].mediaSource,
                        Equalizer.stream_object[tabId].settings,
                        !hardStop
                    );
                };
            } catch (error) {
                reject(error);
            }
        });
    },

    changeVolume: async function (tabId: string, data: any, callback: any = function () { }) {
        Equalizer.stream_object[tabId].settings.volume = data.volume;
        Equalizer.stream_object[tabId].filteredGain.gain.setValueAtTime(Equalizer.stream_object[tabId].settings.volume, Equalizer.stream_object[tabId].audioContext.currentTime);

        Equalizer.stream_object[tabId].settings.mute = data.mute;
        Equalizer.stream_object[tabId].settings.muted_at = data.muted_at;
        callback(Equalizer.stream_object[tabId]);
    },

    changeBaseVolume: async function (tabId: string, volume: any, callback: any = function () { }) {
        Equalizer.stream_object[tabId].settings.base_volume = volume;

        Equalizer.stream_object[tabId].baseGain.gain.setValueAtTime(Equalizer.stream_object[tabId].settings.base_volume, Equalizer.stream_object[tabId].audioContext.currentTime);

        callback(Equalizer.stream_object[tabId]);
    },

    spatialAudio: async function (tabContext: any) {
        let intervalTime = 100; // milliseconds
        let steps;
        let step = 0;

        // Clear the existing interval if it exists
        if (tabContext.spatial_audio_interval) {
            clearInterval(tabContext.spatial_audio_interval);
        }

        tabContext.spatial_audio_interval = setInterval(() => {
            steps = tabContext.settings.spatial_audio.config.duration / intervalTime;
            let t = step / steps;
            let positionX: number, positionY: number, positionZ: number = 0, amplitude: number = 2;

            switch (tabContext.settings.spatial_audio.config.shape) {
                default:
                    let angle = t * 360;
                    amplitude = 2;
                    positionX = Math.sin((angle * Math.PI) / 180) * amplitude;
                    positionY = Math.sin((angle * Math.PI) / 180) * amplitude * Math.cos((angle * Math.PI) / 180);
                    positionZ = Math.cos((angle * Math.PI) / 180) * amplitude;
                    break;

                case 'infinity':
                    let angleInfinity = t * 360;
                    amplitude = 2;
                    positionX = Math.sin((angleInfinity * Math.PI) / 180) * amplitude;
                    positionY = Math.sin((angleInfinity * Math.PI) / 180) * amplitude * Math.cos((angleInfinity * Math.PI) / 180);
                    positionZ = Math.cos((angleInfinity * Math.PI) / 180) * amplitude;
                    break;

                case 'butterfly':
                    let angleButterfly = t * 360;
                    amplitude = 2;
                    positionX = amplitude * Math.sin((angleButterfly * Math.PI) / 180) * (Math.exp(Math.cos((angleButterfly * Math.PI) / 180)) - 2 * Math.cos(4 * (angleButterfly * Math.PI) / 180) - Math.pow(Math.sin((angleButterfly * Math.PI) / 180 / 12), 5));
                    positionY = amplitude * Math.cos((angleButterfly * Math.PI) / 180) * (Math.exp(Math.cos((angleButterfly * Math.PI) / 180)) - 2 * Math.cos(4 * (angleButterfly * Math.PI) / 180) - Math.pow(Math.sin((angleButterfly * Math.PI) / 180 / 12), 5));
                    break;

                case 'hummingbird':
                    let angleHummingbird = t * 360;
                    amplitude = 1;
                    positionX = amplitude * Math.sin((angleHummingbird * Math.PI) / 180) * (Math.exp(Math.cos((angleHummingbird * Math.PI) / 180)) - 2 * Math.cos(4 * (angleHummingbird * Math.PI) / 180) - Math.pow(Math.sin((angleHummingbird * Math.PI) / 180 / 12), 5));
                    positionY = amplitude * Math.cos((angleHummingbird * Math.PI) / 180) * (Math.exp(Math.cos((angleHummingbird * Math.PI) / 180)) - 2 * Math.cos(4 * (angleHummingbird * Math.PI) / 180) - Math.pow(Math.sin((angleHummingbird * Math.PI) / 180 / 12), 5));
                    break;

                case 'bee':
                    let angleBee = t * 360;
                    let amplitudeX = 1; // Amplitude along the X-axis
                    let amplitudeY = 1; // Amplitude along the Y-axis
                    positionX = Math.sin((angleBee * Math.PI) / 180) * amplitudeX;
                    positionY = Math.sin((angleBee * Math.PI) / 180) * amplitudeY * Math.cos((angleBee * Math.PI) / 180);
                    positionZ = Math.cos((angleBee * Math.PI) / 180) * amplitudeX; // Use amplitudeX for the Z-axis as well if you want the full infinity shape                    
                    break;

                case 'triple-knot':
                    let angleTripleKnot = t * 360;
                    amplitude = 1.5;
                    let radiusTripleKnot = 1;
                    let heightTripleKnot = 0.5;

                    let xKnot = radiusTripleKnot * Math.cos((angleTripleKnot * Math.PI) / 180);
                    let yKnot = radiusTripleKnot * Math.sin((angleTripleKnot * Math.PI) / 180);
                    let zKnot = amplitude * Math.sin((2 * angleTripleKnot * Math.PI) / 180) * heightTripleKnot;

                    positionX = xKnot;
                    positionY = yKnot;
                    positionZ = zKnot;
                    break;

                case 'oval':
                    let angleOval = t * 360;
                    amplitude = 2;
                    let radiusXOval = 2;
                    let radiusYOval = 1;
                    positionX = amplitude * radiusXOval * Math.cos((angleOval * Math.PI) / 180);
                    positionY = amplitude * radiusYOval * Math.sin((angleOval * Math.PI) / 180);
                    break;

                case 'wiggly':
                    let angleWiggly = t * 360;
                    amplitude = 3;
                    let frequencyWiggly = 1;
                    let randomnessWiggly = 0.5;
                    let randomOffsetXWiggly = (Math.random() - 0.5) * randomnessWiggly;
                    let randomOffsetYWiggly = (Math.random() - 0.5) * randomnessWiggly;
                    let randomOffsetZWiggly = (Math.random() - 0.5) * randomnessWiggly;
                    positionX = Math.sin((angleWiggly * frequencyWiggly * Math.PI) / 180) * amplitude + randomOffsetXWiggly;
                    positionY =
                        Math.sin((angleWiggly * frequencyWiggly * Math.PI) / 180) * amplitude * Math.cos((angleWiggly * Math.PI) / 180) +
                        randomOffsetYWiggly;
                    positionZ = Math.cos((angleWiggly * frequencyWiggly * Math.PI) / 180) * 2 + randomOffsetZWiggly;
                    break;

                case 'snake':
                    let angleSnake = t * 360;
                    amplitude = 2;
                    let widthFactorSnake = 0.5;
                    positionX = Math.sin((angleSnake * Math.PI) / 180) * amplitude;
                    positionY = Math.sin((angleSnake * Math.PI) / 180) * amplitude * Math.cos((angleSnake * Math.PI) / 180) * widthFactorSnake;
                    positionZ = Math.cos((angleSnake * Math.PI) / 180) * amplitude;
                    break;

                case 'mobius-strip':
                    let angleMobius = t * 360;
                    amplitude = 2;
                    positionX = Math.sin((angleMobius * Math.PI) / 180) * amplitude;
                    positionY = Math.sin((2 * angleMobius * Math.PI) / 180) * amplitude * 0.5;
                    positionZ = Math.cos((2 * angleMobius * Math.PI) / 180) * amplitude;
                    break;

                case 'helix':
                    let angleHelix = t * 360;
                    amplitude = 2;
                    positionX = amplitude * Math.sin((angleHelix * Math.PI) / 180);
                    positionY = amplitude * Math.cos((angleHelix * Math.PI) / 180);
                    positionZ = amplitude * 0.5 * Math.sin((angleHelix * Math.PI) / 180);
                    break;

                case 'hypotrochoid':
                    let angleHypotrochoid = t * 360;
                    let radiusFixed = 2;
                    let radiusMoving = 1;
                    let dHypotrochoid = 0.5;
                    positionX = (radiusFixed - radiusMoving) * Math.cos((angleHypotrochoid * Math.PI) / 180) + dHypotrochoid * Math.cos(((radiusFixed - radiusMoving) / radiusMoving) * angleHypotrochoid * (Math.PI / 180));
                    positionY = (radiusFixed - radiusMoving) * Math.sin((angleHypotrochoid * Math.PI) / 180) - dHypotrochoid * Math.sin(((radiusFixed - radiusMoving) / radiusMoving) * angleHypotrochoid * (Math.PI / 180));
                    positionZ = 0;
                    break;

                case 'lissajous':
                    let angleLissajous = t * 360;
                    let aLissajous = 3;
                    let bLissajous = 2;
                    let deltaLissajous = Math.PI / 2;
                    positionX = Math.sin(aLissajous * (angleLissajous + deltaLissajous) * (Math.PI / 180));
                    positionY = Math.sin(bLissajous * angleLissajous * (Math.PI / 180));
                    positionZ = 0;
                    break;

                case 'figure-8':
                    let angleFigure8 = t * 360;
                    amplitude = 2;
                    positionX = amplitude * Math.sin((angleFigure8 * Math.PI) / 180);
                    positionY = amplitude * Math.sin((2 * angleFigure8 * Math.PI) / 180) * 0.5;
                    positionZ = amplitude * Math.cos((2 * angleFigure8 * Math.PI) / 180);
                    break;

                case 'flower':
                    let angleFlower = t * 360;
                    amplitude = 2;
                    positionX = amplitude * Math.sin((5 * angleFlower * Math.PI) / 180) * Math.sin((angleFlower * Math.PI) / 180);
                    positionY = amplitude * Math.sin((5 * angleFlower * Math.PI) / 180) * Math.cos((angleFlower * Math.PI) / 180);
                    positionZ = amplitude * Math.cos((5 * angleFlower * Math.PI) / 180);
                    break;

                case 'double-helix':
                    let angleDoubleHelix = t * 360;
                    amplitude = 2;
                    positionX = amplitude * Math.sin((angleDoubleHelix * Math.PI) / 180);
                    positionY = amplitude * Math.cos((angleDoubleHelix * Math.PI) / 180);
                    positionZ = amplitude * 0.5 * Math.sin((angleDoubleHelix * Math.PI) / 180);
                    break;

                case 'diamond':
                    let angleDiamond = t * 360;
                    amplitude = 2;
                    positionX = amplitude * Math.sin((angleDiamond * Math.PI) / 180) * Math.cos((angleDiamond * Math.PI) / 180);
                    positionY = amplitude * Math.cos((angleDiamond * Math.PI) / 180) * Math.sin((angleDiamond * Math.PI) / 180);
                    positionZ = amplitude * Math.sin((angleDiamond * Math.PI) / 180);
                    break;

                case 'concentric-circles':
                    let angleCircles = t * 360;
                    amplitude = 2;
                    let radiusCircles = 1 + 0.5 * Math.sin((2 * angleCircles * Math.PI) / 180);
                    positionX = amplitude * radiusCircles * Math.cos((angleCircles * Math.PI) / 180);
                    positionY = amplitude * radiusCircles * Math.sin((angleCircles * Math.PI) / 180);
                    break;

                case 'zigzag':
                    let angleZigzag = t * 360;
                    amplitude = 2;
                    let frequencyZigzag = 2;
                    positionX = amplitude * Math.sin((angleZigzag * frequencyZigzag * Math.PI) / 180);
                    positionY = amplitude * Math.cos((angleZigzag * frequencyZigzag * Math.PI) / 180);
                    break;

                case 'triangle-wave':
                    let angleTriangle = t * 360;
                    amplitude = 2;
                    positionX = amplitude * Math.sin((angleTriangle * Math.PI) / 180);
                    positionY = amplitude * Math.sin((angleTriangle * Math.PI) / 180);
                    break;

                case 'ellipse':
                    let angleEllipse = t * 360;
                    amplitude = 2;
                    let radiusXEllipse = 2;
                    let radiusYEllipse = 1;
                    positionX = amplitude * radiusXEllipse * Math.cos((angleEllipse * Math.PI) / 180);
                    positionY = amplitude * radiusYEllipse * Math.sin((angleEllipse * Math.PI) / 180);
                    break;

                // new ones
                case 'spiral':
                    let angleSpiral = t * 720; // Two full rotations
                    amplitude = 2 * t; // Increasing radius
                    positionX = amplitude * Math.cos((angleSpiral * Math.PI) / 180);
                    positionY = amplitude * Math.sin((angleSpiral * Math.PI) / 180);
                    positionZ = t * 4 - 2; // Move from -2 to 2 on Z-axis
                    break;

                case 'heart':
                    let angleHeart = t * 360;
                    amplitude = 0.1; // Reduced amplitude to bring the shape closer
                    positionX = amplitude * 16 * Math.pow(Math.sin((angleHeart * Math.PI) / 180), 3);
                    positionY = amplitude * (13 * Math.cos((angleHeart * Math.PI) / 180) - 5 * Math.cos(2 * (angleHeart * Math.PI) / 180) - 2 * Math.cos(3 * (angleHeart * Math.PI) / 180) - Math.cos(4 * (angleHeart * Math.PI) / 180));
                    positionZ = (amplitude * Math.sin((angleHeart * Math.PI) / 180)) - 1; // Brings the entire shape 1 unit closer to the listener
                    break;

                case 'rose':
                    let angleRose = t * 360;
                    amplitude = 2;
                    let k = 3; // Number of petals
                    let r = amplitude * Math.cos(k * (angleRose * Math.PI) / 180);
                    positionX = r * Math.cos((angleRose * Math.PI) / 180);
                    positionY = r * Math.sin((angleRose * Math.PI) / 180);
                    positionZ = amplitude * Math.sin(k * (angleRose * Math.PI) / 180);
                    break;

                case 'trefoil-knot':
                    let angleTrefoil = t * 360;
                    amplitude = 2;
                    positionX = amplitude * (Math.sin((angleTrefoil * Math.PI) / 180) + 2 * Math.sin(2 * (angleTrefoil * Math.PI) / 180));
                    positionY = amplitude * (Math.cos((angleTrefoil * Math.PI) / 180) - 2 * Math.cos(2 * (angleTrefoil * Math.PI) / 180));
                    positionZ = amplitude * (-Math.sin(3 * (angleTrefoil * Math.PI) / 180));
                    break;

                case 'cube':
                    let phaseCube = Math.floor(t * 4) % 4; // 4 phases for each face
                    let progressCube = (t * 4) % 1; // Progress within each face
                    amplitude = 2;
                    switch (phaseCube) {
                        case 0: // Bottom face
                            positionX = amplitude * (progressCube * 2 - 1);
                            positionY = -amplitude;
                            positionZ = -amplitude;
                            break;
                        case 1: // Right face
                            positionX = amplitude;
                            positionY = amplitude * (progressCube * 2 - 1);
                            positionZ = -amplitude;
                            break;
                        case 2: // Top face
                            positionX = amplitude * (1 - progressCube * 2);
                            positionY = amplitude;
                            positionZ = -amplitude;
                            break;
                        case 3: // Left face
                            positionX = -amplitude;
                            positionY = amplitude * (1 - progressCube * 2);
                            positionZ = -amplitude;
                            break;
                    }
                    break;

                case 'vortex':
                    let angleVortex = t * 720; // Two full rotations per cycle
                    let radiusVortex = amplitude * (1 - t * 0.5); // Decreasing radius
                    let heightVortex = amplitude * 2 * (1 - t); // Descending height

                    // Spiral motion
                    positionX = radiusVortex * Math.cos((angleVortex * Math.PI) / 180);
                    positionY = radiusVortex * Math.sin((angleVortex * Math.PI) / 180);

                    // Descending motion
                    positionZ = heightVortex - amplitude;

                    // Add subtle wobble
                    let wobbleFrequency = 10;
                    let wobbleAmplitude = 0.1;
                    positionX += wobbleAmplitude * Math.sin(wobbleFrequency * angleVortex * (Math.PI / 180));
                    positionY += wobbleAmplitude * Math.cos(wobbleFrequency * angleVortex * (Math.PI / 180));
                    break;

                case 'celestial-dance':
                    let angleCelestial = t * 360;
                    let radius1 = amplitude * 1.5;
                    let radius2 = amplitude * 0.75;
                    let height = amplitude * 0.5;

                    // Primary orbit
                    let x1 = radius1 * Math.cos((angleCelestial * Math.PI) / 180);
                    let y1 = radius1 * Math.sin((angleCelestial * Math.PI) / 180);

                    // Secondary orbit
                    let x2 = radius2 * Math.cos((2 * angleCelestial * Math.PI) / 180);
                    let y2 = radius2 * Math.sin((2 * angleCelestial * Math.PI) / 180);

                    // Combine orbits with smooth transition
                    let blendFactor = (Math.sin((angleCelestial * Math.PI) / 180) + 1) / 2; // Oscillates between 0 and 1
                    positionX = x1 * (1 - blendFactor) + x2 * blendFactor;
                    positionY = y1 * (1 - blendFactor) + y2 * blendFactor;

                    // Smooth height variation
                    positionZ = height * Math.sin((angleCelestial * Math.PI) / 90); // Two full oscillations

                    // Add gentle swirl
                    let swirlFactor = 0.2;
                    positionX += swirlFactor * Math.sin((3 * angleCelestial * Math.PI) / 180);
                    positionY += swirlFactor * Math.cos((3 * angleCelestial * Math.PI) / 180);
                    break;

            }

            tabContext.spatial_audio.positionX.value = positionX;
            tabContext.spatial_audio.positionY.value = positionY;
            tabContext.spatial_audio.positionZ.value = positionZ;

            step++;

            if (step === steps) {
                step = 0;
            }

        }, intervalTime);
    },


    changeSpatialAudio: async function (tabId: string, data: any, callback: any = function () { }) {
        if (Equalizer.stream_object[tabId]?.settings) {
            if (!Equalizer.stream_object[tabId].spatial_audio || (Equalizer.stream_object[tabId].spatial_audio && !Equalizer.stream_object[tabId].settings.spatial_audio.status)) {
                Equalizer.stream_object[tabId].settings.spatial_audio.status = true;
                Equalizer.reload(tabId).then((e: any) => {
                    Equalizer.stream_object[tabId].settings.spatial_audio.config.shape = data.shape;
                    Equalizer.stream_object[tabId].settings.spatial_audio.config.duration = data.duration;
                });
                Equalizer.stream_object[tabId].settings.spatial_audio.config.shape = data.shape;
                Equalizer.stream_object[tabId].settings.spatial_audio.config.duration = data.duration;
            } else {
                Equalizer.stream_object[tabId].settings.spatial_audio.config.shape = data.shape;
                Equalizer.stream_object[tabId].settings.spatial_audio.config.duration = data.duration;
            }
        }
        callback(Equalizer.stream_object[tabId]);
    },

    stopSpatialAudio: async function (tabId: string, callback: any = function () { }) {
        if (Equalizer.stream_object[tabId].settings.spatial_audio.status) {
            Equalizer.stream_object[tabId].settings.spatial_audio.status = false;
            Equalizer.reload(tabId).then((e: any) => {
                callback(Equalizer.stream_object[tabId]);
            });
        }
    },

    changeEqualizer: async function (tabId: string, knobs: Array<number> = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], callback: any = function () { }) {
        // update settings
        Equalizer.stream_object[tabId].settings.eq = knobs;

        Equalizer.stream_object[tabId].twenty.gain.setValueAtTime(Number(knobs[0]), Equalizer.stream_object[tabId].audioContext.currentTime);
        Equalizer.stream_object[tabId].fifty.gain.setValueAtTime(Number(knobs[1]), Equalizer.stream_object[tabId].audioContext.currentTime);
        Equalizer.stream_object[tabId].oneHundred.gain.setValueAtTime(Number(knobs[2]), Equalizer.stream_object[tabId].audioContext.currentTime);
        Equalizer.stream_object[tabId].twoHundred.gain.setValueAtTime(Number(knobs[3]), Equalizer.stream_object[tabId].audioContext.currentTime);
        Equalizer.stream_object[tabId].fiveHundred.gain.setValueAtTime(Number(knobs[4]), Equalizer.stream_object[tabId].audioContext.currentTime);
        Equalizer.stream_object[tabId].oneThousand.gain.setValueAtTime(Number(knobs[5]), Equalizer.stream_object[tabId].audioContext.currentTime);
        Equalizer.stream_object[tabId].twoThousand.gain.setValueAtTime(Number(knobs[6]), Equalizer.stream_object[tabId].audioContext.currentTime);
        Equalizer.stream_object[tabId].fiveThousand.gain.setValueAtTime(Number(knobs[7]), Equalizer.stream_object[tabId].audioContext.currentTime);
        Equalizer.stream_object[tabId].tenThousand.gain.setValueAtTime(Number(knobs[8]), Equalizer.stream_object[tabId].audioContext.currentTime);
        Equalizer.stream_object[tabId].twentyThousand.gain.setValueAtTime(Number(knobs[9]), Equalizer.stream_object[tabId].audioContext.currentTime);
        callback(Equalizer.stream_object[tabId]);
    },

    changeCompressor: async function (tabId: string, data: any, callback: any = function () { }) {
        // update settings
        Equalizer.stream_object[tabId].settings.compressor.config = data;

        if (!Equalizer.stream_object[tabId].compressor || (Equalizer.stream_object[tabId].compressor && !Equalizer.stream_object[tabId].settings.compressor.status)) {
            Equalizer.stream_object[tabId].settings.compressor.status = true;
            Equalizer.reload(tabId).then((e: any) => {
                Equalizer.stream_object[tabId].compressor.automate('threshold', parseFloat(data.threshold));
                Equalizer.stream_object[tabId].compressor.automate('release', parseFloat(data.release));
                Equalizer.stream_object[tabId].compressor.automate("makeupGain", parseFloat(data.makeupGain))
                Equalizer.stream_object[tabId].compressor.automate('attack', parseFloat(data.attack));
                Equalizer.stream_object[tabId].compressor.automate('ratio', parseFloat(data.ratio));
                Equalizer.stream_object[tabId].compressor.automate('knee', parseFloat(data.knee));
            });
        } else {
            Equalizer.stream_object[tabId].compressor.automate('threshold', parseFloat(data.threshold));
            Equalizer.stream_object[tabId].compressor.automate('release', parseFloat(data.release));
            Equalizer.stream_object[tabId].compressor.automate("makeupGain", parseFloat(data.makeupGain))
            Equalizer.stream_object[tabId].compressor.automate('attack', parseFloat(data.attack));
            Equalizer.stream_object[tabId].compressor.automate('ratio', parseFloat(data.ratio));
            Equalizer.stream_object[tabId].compressor.automate('knee', parseFloat(data.knee));
        }
        callback(Equalizer.stream_object[tabId]);
    },

    stopCompressor: async function (tabId: string, callback: any = function () { }) {
        if (Equalizer.stream_object[tabId].settings.compressor.status) {
            Equalizer.stream_object[tabId].settings.compressor.status = false;
            Equalizer.reload(tabId).then((e: any) => {
                callback(Equalizer.stream_object[tabId]);
            });
        }
    },

    changeReverb: async function (tabId: string, data: any, callback: any = function () { }) {

        // update settings
        Equalizer.stream_object[tabId].settings.tuna_reverb.config = data;

        if (!Equalizer.stream_object[tabId].tuna_reverb || (Equalizer.stream_object[tabId].tuna_reverb && !Equalizer.stream_object[tabId].settings.tuna_reverb.status)) {
            Equalizer.stream_object[tabId].settings.tuna_reverb.status = true;
            Equalizer.reload(tabId).then((e: any) => {
                Equalizer.stream_object[tabId].tuna_reverb.automate('lowCut', parseFloat(data.lowCut));
                Equalizer.stream_object[tabId].tuna_reverb.automate('highCut', parseFloat(data.highCut));
                Equalizer.stream_object[tabId].tuna_reverb.automate('wetLevel', parseFloat(data.wetLevel));
                Equalizer.stream_object[tabId].tuna_reverb.automate('level', parseFloat(data.level));
                Equalizer.stream_object[tabId].tuna_reverb.automate('dryLevel', parseFloat(data.dryLevel));
            });
        } else {
            Equalizer.stream_object[tabId].tuna_reverb.automate('lowCut', parseFloat(data.lowCut));
            Equalizer.stream_object[tabId].tuna_reverb.automate('highCut', parseFloat(data.highCut));
            Equalizer.stream_object[tabId].tuna_reverb.automate('wetLevel', parseFloat(data.wetLevel));
            Equalizer.stream_object[tabId].tuna_reverb.automate('level', parseFloat(data.level));
            Equalizer.stream_object[tabId].tuna_reverb.automate('dryLevel', parseFloat(data.dryLevel));
        }
        callback(Equalizer.stream_object[tabId]);
    },

    stopReverb: async function (tabId: string, callback: any = function () { }) {
        if (Equalizer.stream_object[tabId].settings.tuna_reverb.status) {
            Equalizer.stream_object[tabId].settings.tuna_reverb.status = false;
            Equalizer.reload(tabId).then((e: any) => {
                callback(Equalizer.stream_object[tabId]);
            });
        }
    },

    changeBasicReverb: async function (tabId: string, data: any, callback: any = function () { }) {

        if (!Equalizer.stream_object[tabId].tone_reverb || (Equalizer.stream_object[tabId].tone_reverb && !Equalizer.stream_object[tabId].settings.tone_reverb.status)) {
            Equalizer.stream_object[tabId].settings.tone_reverb.status = true;
            Equalizer.reload(tabId).then((e: any) => {
                Equalizer.stream_object[tabId].tone_reverb.preDelay = parseFloat(data.preDelay);
                Equalizer.stream_object[tabId].tone_reverb.decay = parseFloat(data.decay);
                Equalizer.stream_object[tabId].tone_reverb.wet.value = parseFloat(data.wet);
            });
        } else {
            Equalizer.stream_object[tabId].tone_reverb.preDelay = parseFloat(data.preDelay);
            Equalizer.stream_object[tabId].tone_reverb.decay = parseFloat(data.decay);
            Equalizer.stream_object[tabId].tone_reverb.wet.value = parseFloat(data.wet);
        }

        // update settings
        Equalizer.stream_object[tabId].settings.tone_reverb.config = data;
        callback(Equalizer.stream_object[tabId]);
    },

    stopBasicReverb: async function (tabId: string, callback: any = function () { }) {
        if (Equalizer.stream_object[tabId].settings.tone_reverb.status) {
            Equalizer.stream_object[tabId].settings.tone_reverb.status = false;
            Equalizer.reload(tabId).then((e: any) => {
                callback(Equalizer.stream_object[tabId]);
            });
        }
    },

    changeAudioMode: async function (tabId: string, data: any, callback: any = function () { }) {

        // update settings
        Equalizer.stream_object[tabId].settings.mono = data;
        Equalizer.reload(tabId).then((e: any) => {
            callback(Equalizer.stream_object[tabId]);
        });
    },

    changeChorus: async function (tabId: string, data: any, callback: any = function () { }) {

        // update settings
        Equalizer.stream_object[tabId].settings.chorus.config = data;

        if (!Equalizer.stream_object[tabId].chorus || (Equalizer.stream_object[tabId].chorus && !Equalizer.stream_object[tabId].settings.chorus.status)) {
            Equalizer.stream_object[tabId].settings.chorus.status = true;
            Equalizer.reload(tabId).then((e: any) => {
                Equalizer.stream_object[tabId].chorus.frequency = parseFloat(data.frequency);
                Equalizer.stream_object[tabId].chorus.delayTime = parseFloat(data.delayTime);
                Equalizer.stream_object[tabId].chorus.depth = parseFloat(data.depth);
                Equalizer.stream_object[tabId].chorus.spread = parseFloat(data.spread);
                Equalizer.stream_object[tabId].chorus.wet.value = parseFloat(data.wet);
            });
        } else {
            Equalizer.stream_object[tabId].chorus.frequency = parseFloat(data.frequency);
            Equalizer.stream_object[tabId].chorus.delayTime = parseFloat(data.delayTime);
            Equalizer.stream_object[tabId].chorus.depth = parseFloat(data.depth);
            Equalizer.stream_object[tabId].chorus.spread = parseFloat(data.spread);
            Equalizer.stream_object[tabId].chorus.wet.value = parseFloat(data.wet);
        }
        callback(Equalizer.stream_object[tabId]);
    },

    stopChorus: async function (tabId: string, callback: any = function () { }) {
        if (Equalizer.stream_object[tabId].settings.chorus.status) {
            Equalizer.stream_object[tabId].settings.chorus.status = false;
            Equalizer.reload(tabId).then((e: any) => {
                callback(Equalizer.stream_object[tabId]);
            });
        }
    },

    changeNoise: async function (tabId: string, data: any, callback: any = function () { }) {

        if (Equalizer.stream_object[tabId]?.settings) {
            // update settings
            Equalizer.stream_object[tabId].settings.noise.config.type = data.type;
            Equalizer.stream_object[tabId].settings.noise.config.volume = parseFloat(data.volume);

            if (!Equalizer.stream_object[tabId].noise || (Equalizer.stream_object[tabId].noise && !Equalizer.stream_object[tabId].settings.noise.status)) {
                Equalizer.stream_object[tabId].settings.noise.status = true;
                Equalizer.reload(tabId).then((e: any) => {
                    Equalizer.stream_object[tabId].noise.volume.value = parseFloat(data.volume);
                    Equalizer.stream_object[tabId].noise.type = data.type;
                });
            } else {
                Equalizer.stream_object[tabId].noise.volume.value = parseFloat(data.volume);
                Equalizer.stream_object[tabId].noise.type = data.type;
            }
        }
        callback(Equalizer.stream_object[tabId]);
    },

    stopNoise: async function (tabId: string, callback: any = function () { }) {
        if (Equalizer.stream_object[tabId]?.settings?.noise?.status) {
            Equalizer.stream_object[tabId].settings.noise.status = false;
            Equalizer.stream_object[tabId]?.noise?.stop();
            //Equalizer.stream_object[tabId].noise = false;
        }
        callback(Equalizer.stream_object[tabId]);
    },

    connectVisualizer: async function (tabId: string, callback: any = function () { }) {
        callback(Equalizer.stream_object[tabId]);
    },

    fetchTabSettings: async function (tabId: string, callback: any = function () { }) {
        let callback_settings: any = (Equalizer.stream_object[tabId]?.settings != undefined) ?
            Equalizer.stream_object[tabId].settings :
            ((Equalizer.disconnected_settings[tabId]?.id != undefined) ?
                Equalizer.disconnected_settings[tabId] :
                Equalizer.default_settings);

        callback(callback_settings);
    },

    loadProfile: async function (tabId: string, profile: any, callback: any = function () { }) {

        let is_connected = false;
        if (Equalizer.stream_object[tabId] != undefined) {
            is_connected = Equalizer.stream_object[tabId].settings.connected;
        }

        // update settings
        Equalizer.stream_object[tabId].settings = await Equalizer.configureSettings(profile);
        if (is_connected) {

            //tone_chorus
            if (Equalizer.stream_object[tabId].settings.chorus.status) {
                if (Equalizer.stream_object[tabId].chorus) {
                    Equalizer.stream_object[tabId].settings.chorus.status = false;
                }
                Equalizer.changeChorus(tabId, Equalizer.stream_object[tabId].settings.chorus.config);
            } else {
                if (Equalizer.stream_object[tabId].chorus) {
                    Equalizer.stopChorus(tabId);
                }
            }

            //spatial_audio
            if (Equalizer.stream_object[tabId].settings.spatial_audio.status) {
                if (Equalizer.stream_object[tabId].spatial_audio) {
                    Equalizer.stream_object[tabId].settings.spatial_audio.status = false;
                }
                Equalizer.changeSpatialAudio(tabId, Equalizer.stream_object[tabId].settings.spatial_audio.config);
            } else {
                if (Equalizer.stream_object[tabId].spatial_audio) {
                    Equalizer.stopSpatialAudio(tabId);
                }
            }

            //tone_reverb
            if (Equalizer.stream_object[tabId].settings.tone_reverb.status) {
                if (Equalizer.stream_object[tabId].tone_reverb) {
                    Equalizer.stream_object[tabId].settings.tone_reverb.status = false;
                }
                Equalizer.changeBasicReverb(tabId, Equalizer.stream_object[tabId].settings.tone_reverb.config);
            } else {
                if (Equalizer.stream_object[tabId].tone_reverb) {
                    Equalizer.stopBasicReverb(tabId);
                }
            }

            //tuna_reverb
            if (Equalizer.stream_object[tabId].settings.tuna_reverb.status) {
                if (Equalizer.stream_object[tabId].tuna_reverb) {
                    Equalizer.stream_object[tabId].settings.tuna_reverb.status = false;
                }

                // it only works this way for some magical reason ... still dk 
                Equalizer.changeReverb(tabId, Equalizer.stream_object[tabId].settings.tuna_reverb.config);
                Equalizer.changeReverb(tabId, Equalizer.stream_object[tabId].settings.tuna_reverb.config);
            } else {
                if (Equalizer.stream_object[tabId].tuna_reverb) {
                    // it only works this way for some magical reason ... still dk 
                    Equalizer.stopReverb(tabId);
                    Equalizer.stopReverb(tabId);
                }
            }

            //equalizer
            Equalizer.changeEqualizer(tabId, Equalizer.stream_object[tabId].settings.eq);

            //compressor
            if (Equalizer.stream_object[tabId].settings.compressor.status) {
                if (Equalizer.stream_object[tabId].compressor) {
                    Equalizer.stream_object[tabId].settings.compressor.status = false;
                }
                Equalizer.changeCompressor(tabId, Equalizer.stream_object[tabId].settings.compressor.config);
            } else {
                if (Equalizer.stream_object[tabId].compressor) {
                    Equalizer.stopCompressor(tabId);
                }
            }

            Equalizer.reload(tabId).then((e: any) => {
                callback(Equalizer.stream_object[tabId]);
            });
        }
    },

}

export default Equalizer;