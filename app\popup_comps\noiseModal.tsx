import Basic from '../Basic';
import React from 'react';
import Box from '@mui/joy/Box';
import Slider from '@mui/joy/Slider';
import Button from '@mui/joy/Button';
import Grid from '@mui/joy/Grid';
import Tooltip from '@mui/joy/Tooltip';
import IconButton from '@mui/joy/IconButton';
import Typography from '@mui/joy/Typography';
import { Transition } from 'react-transition-group';
import Modal from '@mui/joy/Modal';
import ModalDialog from '@mui/joy/ModalDialog';
import DialogTitle from '@mui/joy/DialogTitle';
import DialogContent from '@mui/joy/DialogContent';
import Select from '@mui/joy/Select';
import Option from '@mui/joy/Option';


// Icons
import ResetIcon from '@mui/icons-material/RotateLeft';
import NoiseIcon from '@mui/icons-material/Hearing';
import NoiseTypeIcon from '@mui/icons-material/Water';

const NoiseModal = function ({ eq_knobs, open, setOpen, noiseStatus, setNoiseStatus, noise, setNoise, callback = () => { }, sendStatus, resetStatus, sendMessage, currentTab, knobs }) {
    const arraysEqual = async (arr1: any, arr2: any) => {
        return arr1.length === arr2.length && arr1.every((value, index) => value === arr2[index]);
    }

    const handleTypeChange = async (value) => {
        handleChange(0, value);
    }
    const handleVolumeChange = async (ele: any, value) => {
        handleChange(1, value);
    }

    // Function to update the global variable and state when a slider changes
    const handleChange = async (id: number, value: number | string) => {

        const updatedValues = [...eq_knobs.values];
        updatedValues[id] = value;
        eq_knobs.values = updatedValues; // Update the global values

        // Update the state to trigger a re-render
        setNoise(updatedValues);
        if (!noiseStatus) {
            setNoiseStatus(true);
            if (!eq_knobs.active) {
                eq_knobs.active = true;
            }
        }

        if (currentTab != null && sendStatus) {
            sendMessage({
                type: eq_knobs.event_name,
                target: 'offscreen',
                tabid: currentTab.id,
                data: [
                    updatedValues[0],
                    updatedValues[1]
                ]
            });
        }
    };

    if (noiseStatus) {
        var oldKnob = [...knobs[eq_knobs.key].values];
        eq_knobs.values = noise

        if ((sendStatus && !arraysEqual(oldKnob, noise)) || resetStatus) {
            sendMessage({
                type: eq_knobs.event_name,
                target: 'offscreen',
                tabid: currentTab.id,
                data: [
                    noise[0],
                    noise[1]
                ]
            });
        }
    }

    function knobParams(params, index = 0) {
        return Array.isArray(params) ? params[index] : params;
    }

    function resetVolume() {
        setNoise([noise[0], eq_knobs.default[1]]);
        handleVolumeChange(1, eq_knobs.default[1]);
    }

    function toggleNoise() {
        if (noiseStatus) {
            setNoiseStatus(false);
            sendMessage({
                type: 'stop-effect',
                target: 'offscreen',
                tabid: currentTab.id,
                data: 'noise'
            });
        } else {
            setNoiseStatus(true);
            handleTypeChange(noise[0]);
        }
    }

    return (
        <React.Fragment>
            <Transition in={open} timeout={100}>
                {(state: string) => (
                    <Modal
                        keepMounted
                        open={!['exited', 'exiting'].includes(state)}
                        onClose={() => setOpen(false)}
                        slotProps={{
                            backdrop: {
                                sx: {
                                    opacity: 0,
                                    backdropFilter: 'none',
                                    transition: `opacity 200ms, backdrop-filter 200ms`,
                                    ...{
                                        entering: { opacity: 1, backdropFilter: 'blur(8px)' },
                                        entered: { opacity: 1, backdropFilter: 'blur(8px)' },
                                    }[state],
                                },
                            },
                        }}
                        sx={{
                            visibility: state === 'exited' ? 'hidden' : 'visible',
                        }}
                    >
                        <ModalDialog
                            sx={{
                                overflow: 'hidden',
                                opacity: 0,
                                transition: `opacity 300ms`,
                                ...{
                                    entering: { opacity: 1 },
                                    entered: { opacity: 1 },
                                }[state],
                            }}
                        >
                            <DialogTitle>{Basic.l('background_noise')}</DialogTitle>
                            <DialogContent sx={{ overflow: 'hidden' }}>
                                <Typography level="body-xs">{Basic.l('background_noise_subtitle')}</Typography>
                                <Box sx={{ display: 'flex', gap: 1, alignItems: 'center', pt: 1 }}>
                                    <Button
                                        color="neutral"
                                        variant="soft"
                                        onClick={toggleNoise}
                                        sx={{
                                            '& svg': {
                                                color: noiseStatus ? Basic.colors.green : ''
                                            },
                                            '&:hover svg': {
                                                color: Basic.colors.green
                                            }
                                        }}
                                    >
                                        {(noiseStatus) && (
                                            <React.Fragment>
                                                <NoiseIcon sx={{ px: 1 }} /> {Basic.l('stop')}
                                            </React.Fragment>
                                        )}
                                        {(!noiseStatus) && (
                                            <React.Fragment>
                                                <NoiseIcon sx={{ px: 1 }} /> {Basic.l('start')}
                                            </React.Fragment>
                                        )}
                                    </Button>
                                    <Select
                                        placeholder="Select a pet…"
                                        startDecorator={<NoiseTypeIcon fontSize='small' />}
                                        sx={{ width: 240 }}
                                        value={noise[0]}
                                        onChange={(event, newValue) => {
                                            handleTypeChange(newValue);
                                        }}
                                    >
                                        {knobs.noise.presets.types.map((item) => (
                                            <Option key={item} value={item} >{item}</Option>
                                        ))}
                                    </Select>
                                </Box>
                                <Box sx={{ overflow: 'hidden', width: '100%', margin: 0, padding: 0, py: 1.8 }}>
                                    <Grid container spacing={0} sx={{ flexGrow: 1 }}>
                                        <Grid xs="auto"
                                            display="flex"
                                            justifyContent="center"
                                            alignItems="center"
                                        >
                                            <Typography level="body-xs" sx={{ px: 1 }}>
                                                {Basic.l('volume')}
                                            </Typography>
                                        </Grid>
                                        <Grid xs
                                            display="flex"
                                            justifyContent="center"
                                            alignItems="center"
                                        >
                                            <Tooltip title={Basic.l('reset_default_volume')} color="neutral" size="sm" placement="top" variant="soft">
                                                <IconButton
                                                    onClick={resetVolume}
                                                    sx={{ ml: Basic.l('extensionDirection') == 'rtl' ? 1 : 0, mr: Basic.l('extensionDirection') == 'rtl' ? 0 : 1 }} >
                                                    <ResetIcon />
                                                </IconButton>
                                            </Tooltip>
                                            <Slider
                                                color="primary"
                                                variant="solid"
                                                min={knobParams(eq_knobs.min)}
                                                max={knobParams(eq_knobs.max)}
                                                step={knobParams(eq_knobs.step)}
                                                value={noise[1]}
                                                orientation='horizontal'
                                                aria-label="Noise"
                                                valueLabelDisplay="auto"
                                                sx={{
                                                    "--Slider-trackSize": "10px",
                                                    "--Slider-thumbSize": "24px",
                                                    "--Slider-thumbWidth": "24px",
                                                    "--Slider-thumbColor": "#2eff5a !important",
                                                    "--Slider-thumbBackground": "#2eff5a",
                                                    "--Slider-valueLabelArrowSize": "0px",
                                                    pb: 0
                                                }}
                                                onChange={handleVolumeChange}
                                            />
                                        </Grid>
                                    </Grid>
                                </Box>
                            </DialogContent>
                        </ModalDialog>
                    </Modal>
                )}
            </Transition>
        </React.Fragment>
    );
}

export default NoiseModal;