import Basic from './app/Basic';
import React, { Suspense, useState, useEffect } from 'react';
import { CssVarsProvider } from '@mui/joy/styles';
import theme from './app/Theme';
import "./app/css/popup.css";
import svg_icon from 'url:assets/icon.svg';
import Sheet from '@mui/joy/Sheet';
import LinearProgress from '@mui/joy/LinearProgress';

const EqualizerDash = React.lazy(() => import('./app/equalizer_dash'));
var loadingScreen = null;

const LoadingFallback = () => {
    const [progress, setProgress] = useState(0);

    useEffect(() => {
        loadingScreen = setInterval(() => {
            setProgress((oldProgress) => {
                if (oldProgress === 100) {
                    clearInterval(loadingScreen);
                    return 100;
                }
                const diff = Math.random() * 10;
                return Math.min(oldProgress + diff, 100);
            });
        }, 1);
    }, []);

    const blurValue = 5 * (1 - progress / 100);

    return (
        <Sheet className="loader">
            <div className="loader-wrapper">
                <div>
                    <img
                        src={svg_icon}
                        style={{ 
                            'width': '4rem', 
                            'height': '4rem',
                            'filter': `blur(${blurValue}px)`
                        }}
                    />
                </div>
                <div style={{ 'width': '9rem' }}>
                    <LinearProgress 
                        determinate 
                        variant="soft"
                        value={progress} 
                        thickness={1}
                        sx={{ 
                            width: '100%', 
                            mt: 5,
                            "--LinearProgress-progressRadius": "10px",
                            'filter': `blur(${blurValue}px)`
                        }} 
                    />
                </div>
            </div>
        </Sheet>
    );
};

const LoadedMark = () => {
    useEffect(() => {
        if(loadingScreen != null){
            clearInterval(loadingScreen);
        }
    }, []);

    return (<></>);
};

export default function PopupAction() {
    return (
        <CssVarsProvider defaultMode="system" theme={theme}>
            <div
                dir={Basic.l('extensionDirection')}
                style={{ margin: "0px", padding: "0px", width: "580px", minHeight: "584.25px" }}
                className="main_popup no-select"
            >
                <Suspense fallback={<LoadingFallback />} >
                    <EqualizerDash />
                    <LoadedMark />
                </Suspense>
            </div>
        </CssVarsProvider>
    );
}