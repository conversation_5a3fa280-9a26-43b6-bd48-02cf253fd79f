import Basic from '../Basic';
import React from 'react';
import Box from '@mui/joy/Box';
import Divider from '@mui/joy/Divider';
import Chip from '@mui/joy/Chip';
import Card from '@mui/joy/Card';
import Grid from '@mui/joy/Grid';
import Typography from '@mui/joy/Typography';
import { Transition } from 'react-transition-group';
import Modal from '@mui/joy/Modal';
import ModalDialog from '@mui/joy/ModalDialog';
import DialogTitle from '@mui/joy/DialogTitle';
import DialogContent from '@mui/joy/DialogContent';

// Icons
import SpeakerIcon from '@mui/icons-material/Speaker';

const AudioModeModal = function ({ eq_knobs, open, setOpen, audioMode, setAudioMode, callback = () => { }, sendStatus, resetStatus, sendMessage, currentTab, tabSettings }: any) {

    const handleChange = async (value: boolean) => {

        const updatedValue = value;
        eq_knobs.value = value; // Update the global values

        // Update the state to trigger a re-render
        setAudioMode(updatedValue);

        if (currentTab != null && sendStatus && tabSettings.connected) {
            sendMessage({
                type: eq_knobs.event_name,
                target: 'offscreen',
                tabid: currentTab.id,
                data: eq_knobs.value
            });
        }
    };

    eq_knobs.value = audioMode
    if (sendStatus && tabSettings.connected) {
        sendMessage({
            type: eq_knobs.event_name,
            target: 'offscreen',
            tabid: currentTab.id,
            data: eq_knobs.value
        });
    }

    return (
        <React.Fragment>
            <Transition in={open} timeout={100}>
                {(state: string) => (
                    <Modal
                        keepMounted
                        open={!['exited', 'exiting'].includes(state)}
                        onClose={() => setOpen(false)}
                        slotProps={{
                            backdrop: {
                                sx: {
                                    opacity: 0,
                                    backdropFilter: 'none',
                                    transition: `opacity 200ms, backdrop-filter 200ms`,
                                    ...{
                                        entering: { opacity: 1, backdropFilter: 'blur(8px)' },
                                        entered: { opacity: 1, backdropFilter: 'blur(8px)' },
                                    }[state],
                                },
                            },
                        }}
                        sx={{
                            visibility: state === 'exited' ? 'hidden' : 'visible',
                        }}
                    >
                        <ModalDialog
                            sx={{
                                overflow: 'hidden',
                                opacity: 0,
                                transition: `opacity 300ms`,
                                ...{
                                    entering: { opacity: 1 },
                                    entered: { opacity: 1 },
                                }[state],
                            }}
                        >
                            <DialogTitle>{Basic.l('output_mode')}</DialogTitle>
                            <DialogContent sx={{ overflow: 'hidden' }}>
                                <Typography level="body-xs">{Basic.l('output_mode_subtitle')}</Typography>
                                <Grid sx={{ mt: 1 }} container spacing={2} >
                                    <Grid xs={6}>
                                        <Card
                                            onClick={() => handleChange(false)}
                                            sx={{
                                                border: 1,
                                                p: 1.5,
                                                borderColor: !audioMode ? Basic.colors.green : 'background.level2',
                                                alignItems: 'center',
                                                cursor: 'pointer'
                                            }}
                                            variant="soft"
                                            color="neutral" >
                                            <Chip size="sm" sx={{ px: 1 }} variant={!audioMode ? 'outlined' : 'soft'}>{Basic.l('stereo')}</Chip>
                                            <Divider orientation="horizontal" />
                                            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                                                <SpeakerIcon sx={{ px: 1.5 }} />
                                                <Divider orientation="vertical" />
                                                <SpeakerIcon sx={{ px: 1.5 }} />
                                            </Box>
                                        </Card>
                                    </Grid>
                                    <Grid xs={6}>
                                        <Card
                                            onClick={() => handleChange(true)}
                                            sx={{
                                                border: 1,
                                                p: 1.5,
                                                borderColor: audioMode ? Basic.colors.green : 'background.level2',
                                                alignItems: 'center',
                                                cursor: 'pointer'
                                            }}
                                            variant="soft"
                                            color="neutral" >
                                            <Chip size="sm" variant={audioMode ? 'outlined' : 'soft'}>{Basic.l('mono')}</Chip>
                                            <Divider orientation="horizontal" />
                                            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                                                <SpeakerIcon sx={{ px: 1.5 }} />
                                            </Box>
                                        </Card>
                                    </Grid>
                                </Grid>
                            </DialogContent>
                        </ModalDialog>
                    </Modal>
                )}
            </Transition>
        </React.Fragment>
    );
}

export default AudioModeModal;