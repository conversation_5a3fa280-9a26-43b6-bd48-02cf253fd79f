const switchProfile = async (key: string, setProfilesList: any, loadProfile: any, profileManager: any, sendMessage: any, currentTab: any) => {
    await profileManager.switchProfile(key, (profile, latestProfilesList) => {
        loadProfile(profile);
        sendMessage({
            type: 'load-profile',
            target: 'offscreen',
            tabid: currentTab.id,
            data: profile
        });
        setProfilesList(latestProfilesList);
    });
};

export default switchProfile;