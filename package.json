{"name": "pro-equalizer", "displayName": "__MSG_extensionName__", "version": "1.0.6", "description": "__MSG_extensionDescription__", "author": "Viiny", "scripts": {"dev": "plasmo dev", "build": "plasmo build", "test": "plasmo test"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fontsource/inter": "^5.2.6", "@mui/icons-material": "5.15.4", "@mui/joy": "5.0.0-beta.52", "@mui/material": "5.15.4", "@plasmohq/storage": "^1.15.0", "axios": "^1.11.0", "plasmo": "0.90.5", "qrcode": "^1.5.4", "react": "19.1.1", "react-dom": "19.1.1", "react-scrollbars-custom": "^4.1.1", "react-transition-group": "^4.4.5", "tone": "^15.1.22", "tunajs": "^1.0.15"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.1", "@types/chrome": "0.0.256", "@types/node": "20.11.0", "@types/react": "18.2.47", "@types/react-dom": "18.2.18", "prettier": "3.2.1", "typescript": "5.3.3"}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "pnpm": {"peerDependencyRules": {"allowedVersions": {"svgo": "2.8.0"}, "ignoreMissing": ["svgo@^3.0.2"]}}, "manifest": {"short_name": "Equalizer", "default_locale": "en", "homepage_url": "$PLASMO_PUBLIC_SITE_URL", "host_permissions": ["https://www.google-analytics.com/*", "https://*/*"], "permissions": ["activeTab", "tabCapture", "tabs", "offscreen", "storage"], "web_accessible_resources": [{"resources": ["*"], "matches": ["<all_urls>"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; worker-src 'self' 'wasm-unsafe-eval';"}, "externally_connectable": {"matches": ["*://api.viiny.com/*", "*://viiny.com/*"]}}}