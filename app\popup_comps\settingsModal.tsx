import Basic from '../Basic';
import React, { useState, useEffect, useCallback } from 'react';
import Item from '../Item';
import Box from '@mui/joy/Box';
import Divider from '@mui/joy/Divider';
import Button from '@mui/joy/Button';
import Card from '@mui/joy/Card';
import Grid from '@mui/joy/Grid';
import Typography from '@mui/joy/Typography';
import Link from '@mui/joy/Link';
import DialogTitle from '@mui/joy/DialogTitle';
import DialogContent from '@mui/joy/DialogContent';
import { Scrollbar } from 'react-scrollbars-custom';
import Drawer from '@mui/joy/Drawer';

// Icons
import InfoOutlined from '@mui/icons-material/InfoOutlined';
import svg_icon from 'url:assets/icon.svg';
import LegendaryIcon from '@mui/icons-material/Verified';
import BackupIcon from '@mui/icons-material/BrowserUpdated';
import RestoreIcon from '@mui/icons-material/UploadFile';
import EditIcon from '@mui/icons-material/Edit';
import BugReportIcon from '@mui/icons-material/BugReport';
import RecommendIcon from '@mui/icons-material/Recommend';

const SettingsModal = function ({ open, setOpen, anchor, size, setProfilesList, setProfilesModalStatus, profileManager }) {
    const [membership, setMembership] = useState('');
    const [isExporting, setIsExporting] = useState(false);
    const [isImporting, setIsImporting] = useState(false);
  
    useEffect(() => {
      const fetchMembershipType = async () => {
        const type = await Basic.getMembershipType();
        setMembership(type);
      };
  
      fetchMembershipType();
    }, []);
  
    const handleImport = useCallback(() => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';
      input.onchange = handleImportFileChange;
      input.click();
    }, []);
  
    const handleImportFileChange = useCallback((event) => {
      const file = event.target.files[0];
      if (file && file.name.endsWith('.json')) {
        setIsImporting(true);
        const reader = new FileReader();
        reader.onload = async (e) => {
          const fileContent = e.target.result;
          const importStatus = await profileManager.importProfiles(fileContent.toString());
          if (importStatus) {
            setProfilesList(await profileManager.listProfiles());
            setTimeout(() => {
              setIsImporting(false);
              setOpen(false);
              setProfilesModalStatus(true);
            }, 1000);
          } else {
            setIsImporting(false);
          }
        };
        reader.readAsText(file);
      } else {
        setIsImporting(false);
      }
    }, [profileManager, setProfilesList, setProfilesModalStatus, setOpen]);
  
    const handleExportProfiles = useCallback(async () => {
      setIsExporting(true);
      await profileManager.downloadProfiles();
      setTimeout(() => setIsExporting(false), 1000);
    }, [profileManager]);
  
    const openTab = useCallback((url) => {
      chrome.tabs.create({ url });
    }, []);

    return (
        <React.Fragment>
            <Drawer
                anchor={['top', 'bottom', 'left', 'right'].includes(anchor) ? anchor : 'bottom'}
                size={['sm', 'md', 'lg'].includes(size) ? size : 'md'}
                open={open}
                onClose={() => setOpen(false)} >

                <Grid container sx={{ flexGrow: 1 }}>
                    <Grid xs={3}>
                        <Item><DialogTitle sx={{ px: 0.5, fontSize: '15px', lineHeight: '35px' }}>{Basic.l('settings')}</DialogTitle></Item>
                    </Grid>
                    <Grid xs>
                        <Item>
                            <Box sx={{ display: 'flex', gap: 0.5, alignItems: Basic.l('extensionDirection') == 'rtl' ? 'left' : 'right', justifyContent: Basic.l('extensionDirection') == 'rtl' ? 'left' : 'right' }} >
                                <Button
                                    loading={false}
                                    variant="plain"
                                    color="neutral"
                                    sx={{
                                        '& svg': {
                                            color: Basic.colors.green,
                                        },
                                    }}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        chrome.tabs.create({
                                            url: "./tabs/configure.html"
                                        });
                                    }}
                                >
                                    <LegendaryIcon fontSize="small" sx={{ pr: Basic.l('extensionDirection') == 'rtl' ? 0 : 1, pl: Basic.l('extensionDirection') == 'rtl' ? 1 : 0 }} />
                                    {membership == 'premium' ? Basic.l('premium_supporter') : Basic.l('legendary_supporter')}
                                </Button>
                            </Box>
                        </Item>
                    </Grid>
                </Grid>

                <DialogContent>
                    <Scrollbar>

                        <Box sx={{ mb: 1, px: 1, mt: 1 }}>
                            <Card sx={{ px: 1.5, py: 1 }} variant="soft">
                                <Typography component={'span'} startDecorator={<img
                                    src={svg_icon}
                                    style={{ 'width': '3.1rem', 'height': '3.1rem' }}
                                />} level="body-lg" >
                                    <Typography sx={{ px: 1 }} level="body-md" noWrap>
                                        {Basic.l('extensionName')}
                                        <Divider sx={{ opacity: 0 }} />
                                        <Typography level="body-sm" noWrap sx={{
                                            opacity: 0.6,
                                            fontSize: "sm",
                                            lineHeight: "xs",
                                            display: "block"
                                        }} >
                                            {Basic.l('version')}: {Basic.version()}
                                        </Typography>
                                    </Typography>
                                </Typography>
                            </Card>
                        </Box>

                        <Box sx={{ mb: 1, px: 1, mt: 1 }}>
                            <Typography sx={{ mb: 0.5 }} level="body-sm">
                                {Basic.l('send_feedback')}
                            </Typography>
                            <Grid container spacing={2} rowSpacing={0.5} sx={{ flexGrow: 1 }} >
                                <Grid xs={6}>
                                    <Button
                                        variant="soft"
                                        color="neutral"
                                        fullWidth
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            chrome.tabs.create({
                                                url: Basic.apiEndpoint('feedback')
                                            });
                                        }}
                                    >
                                        <RecommendIcon sx={{ px: 1 }} /> {Basic.l('suggest_feature')}
                                    </Button>
                                </Grid>
                                <Grid xs={6}>
                                    <Button
                                        variant="soft"
                                        color="neutral"
                                        fullWidth
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            chrome.tabs.create({
                                                url: Basic.apiEndpoint('report-bug')
                                            });
                                        }}
                                    >
                                        <BugReportIcon sx={{ px: 1 }} /> {Basic.l('report_bug')}
                                    </Button>
                                </Grid>
                            </Grid>
                        </Box>

                        <Box sx={{ mb: 1, px: 1, mt: 1 }}>
                            <Typography sx={{ mb: 0.5 }} level="body-sm">
                                {Basic.l('support_plan')}
                            </Typography>
                            <Grid container spacing={2} rowSpacing={0.5} sx={{ flexGrow: 1 }} >
                                <Grid xs={12}>
                                    <Button
                                        variant="soft"
                                        color="neutral"
                                        fullWidth
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            chrome.tabs.create({
                                                url: "./tabs/configure.html"
                                            });
                                        }}
                                    >
                                        <EditIcon sx={{ px: 1 }} />
                                        {membership == 'premium' ? Basic.l('premium_supporter') : Basic.l('legendary_supporter')}
                                    </Button>
                                </Grid>
                            </Grid>
                        </Box>

                        <Box sx={{ mb: 1.5, px: 1, mt: 1 }}>
                            <Typography sx={{ mb: 0.5 }} level="body-sm">
                                {Basic.l('profiles')}
                            </Typography>
                            <Grid container spacing={2} rowSpacing={0.5} sx={{ flexGrow: 1 }} >
                                <Grid xs={6}>
                                    <Button
                                        variant="soft"
                                        color="neutral"
                                        fullWidth
                                        loading={isExporting}
                                        onClick={async () => {
                                            setIsExporting(true);
                                            await profileManager.downloadProfiles();
                                            setTimeout(() => setIsExporting(false), 1000);
                                        }}
                                    >
                                        <BackupIcon sx={{ px: 1 }} /> {Basic.l('backup_to_file')}
                                    </Button>
                                </Grid>
                                <Grid xs={6}>
                                    <Button
                                        variant="soft"
                                        color="neutral"
                                        fullWidth
                                        loading={isImporting}
                                        onClick={handleImport}
                                    >
                                        <RestoreIcon sx={{ px: 1 }} /> {Basic.l('restore_from_file')}
                                    </Button>
                                </Grid>
                            </Grid>
                        </Box>
                    </Scrollbar>
                </DialogContent>
                <Box
                    sx={{
                        display: 'flex',
                        gap: 1,
                        p: 1,
                        borderTop: '1px solid',
                        borderColor: 'divider',
                    }}
                >
                    <Typography component={'span'} sx={{ fontSize: '12px', opacity: 0.8 }} startDecorator={<InfoOutlined sx={{ fontSize: '15px', px: 0.5 }} />}>
                        <Link
                            href={Basic.privacy_url()}
                            target='_blank'
                        >{' ' + Basic.l('privacy_policy').toLowerCase()}</Link>
                        <Divider sx={{ mx: 1 }} orientation="vertical" />
                        <Link
                            href={Basic.terms_url()}
                            target='_blank'
                        >{' ' + Basic.l('terms_of_service').toLowerCase()}</Link>
                        <Divider sx={{ mx: 1 }} orientation="vertical" />
                        <Link
                            href={Basic.eula_url()}
                            target='_blank'
                        >{' ' + Basic.l('eula').toLowerCase()}</Link>
                    </Typography>
                </Box>
            </Drawer>
        </React.Fragment>
    );
}

export default SettingsModal;