import Sheet from '@mui/joy/Sheet';
import { extendTheme, styled } from '@mui/joy/styles';

const theme = extendTheme({
    "colorSchemes": {
        "light": {
            "palette": {
                "primary": {
                    50: "#E2E7E9",
                    100: "#C9D0D4",
                    200: "#92A2AA",
                    300: "#60727B",
                    400: "#364045",
                    500: "#0B0D0E",
                    600: "#090B0B",
                    700: "#070809",
                    800: "#040506",
                    900: "#020303",
                }
            }
        },
        "dark": {
            "palette": {
                "primary": {
                    50: "#D1D1D1",
                    100: "#D6D6D6",
                    200: "#E0E0E0",
                    300: "#EBEBEB",
                    400: "#F5F5F5",
                    500: "#FFFFFF",
                    600: "#CCCCCC",
                    700: "#999999",
                    800: "#666666",
                    900: "#333333",
                }
            }
        }
    }
});

export default theme;