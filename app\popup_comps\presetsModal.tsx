import Basic from '../Basic';
import React, { useState, useEffect } from 'react';
import Item from '../Item';;
import Box from '@mui/joy/Box';
import Grid from '@mui/joy/Grid';
import Typography from '@mui/joy/Typography';
import DialogTitle from '@mui/joy/DialogTitle';
import DialogContent from '@mui/joy/DialogContent';
import { Scrollbar } from 'react-scrollbars-custom';
import Drawer from '@mui/joy/Drawer';
import List from '@mui/joy/List';
import ListItem from '@mui/joy/ListItem';
import ListItemButton from '@mui/joy/ListItemButton';

// Icons
import GraphicEqIcon from '@mui/icons-material/GraphicEq';


const PresetsModal = function ({ open, setOpen, anchor, size, presets, setResetStatus, setPreset, currentPreset = [] }) {

    const [selectedPreset, setSelectedPreset] = useState(currentPreset);

    useEffect(() => {
        setSelectedPreset(currentPreset);
    }, [currentPreset]);

    function handleClick(preset) {
        setResetStatus(true);
        setPreset(preset);
        if (currentPreset != preset) {
            setSelectedPreset(preset);
        }
        setPreset(preset);
        setTimeout(() => {
            setResetStatus(false);
        }, 50);
    }

    function isSelected(val: any) {
        return JSON.stringify(selectedPreset) === JSON.stringify(val);
    }

    return (
        <React.Fragment>
            <Drawer
                anchor={['top', 'bottom', 'left', 'right'].includes(anchor) ? anchor : 'bottom'}
                size={['sm', 'md', 'lg'].includes(size) ? size : 'md'}
                open={open}
                onClose={() => setOpen(false)} >
                <Grid container sx={{ flexGrow: 1 }}>
                    <Grid xs={3}>
                        <Item><DialogTitle sx={{ px: 0.5, fontSize: '15px', lineHeight: '35px' }}>{Basic.l('presets')}</DialogTitle></Item>
                    </Grid>
                    <Grid xs>
                        <Item>

                        </Item>
                    </Grid>
                </Grid>

                <DialogContent>
                    <Scrollbar>
                        <Box sx={{ px: 1 }}>
                            <List sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 1 }}>
                                {Object.entries(presets).map(([index, preset]: [any, any]) => (
                                    <ListItem
                                        onClick={() => {
                                            handleClick(preset.preset)
                                        }}
                                        key={index}
                                    >
                                        <ListItemButton sx={{
                                            borderRadius: '7px',
                                            py: 0.7,
                                            mb: 0.5,
                                            backgroundColor: isSelected(preset.preset) ? 'background.level1' : 'transparent',
                                            '&:hover .plugIcon': {
                                                color: Basic.colors.green,
                                            },
                                            border: 0,
                                            borderLeft: Basic.l('extensionDirection') == 'rtl' ? 'none' : '2px solid',
                                            borderRight: Basic.l('extensionDirection') == 'rtl' ? '2px solid' : 'none',
                                            borderColor: isSelected(preset.preset) ? Basic.colors.green : 'transparent'
                                        }}>
                                            <Typography startDecorator={<GraphicEqIcon sx={{ color: isSelected(preset.preset) ? Basic.colors.green : '' }} className="plugIcon" fontSize="small" />} noWrap sx={{ width: '100%', fontSize: 'md', fontWeight: 'md' }}>
                                                {preset.name}
                                            </Typography>

                                        </ListItemButton>
                                    </ListItem>
                                ))}
                            </List>

                        </Box>
                    </Scrollbar>
                </DialogContent>
            </Drawer>
        </React.Fragment>
    );
}

export default PresetsModal;