import Basic from './Basic';
import ProfileManager from './ProfileManager';
import UserProfile from '~app/UserProfile';
import Presets from '~app/Presets';
import React, { useState, useRef, useEffect, Suspense, useMemo, useCallback } from 'react';
import { CssVarsProvider, useColorScheme } from '@mui/joy/styles';
import theme from './Theme';
import Item from './Item';
import "~./app/css/popup.css";
import Box from '@mui/joy/Box';
import Slider from '@mui/joy/Slider';
import Divider from '@mui/joy/Divider';
import Tabs from '@mui/joy/Tabs';
import TabList from '@mui/joy/TabList';
import Tab, { tabClasses } from '@mui/joy/Tab';
import TabPanel from '@mui/joy/TabPanel';
import Sheet from '@mui/joy/Sheet';
import Stack from '@mui/joy/Stack';
import Chip from '@mui/joy/Chip';
import Button from '@mui/joy/Button';
import { Visualizer } from './Visualizer';
import ButtonGroup from '@mui/joy/ButtonGroup';
import Card from '@mui/joy/Card';
import Grid from '@mui/joy/Grid';
import Tooltip from '@mui/joy/Tooltip';
import AspectRatio from '@mui/joy/AspectRatio';
import CardCover from '@mui/joy/CardCover';
import IconButton from '@mui/joy/IconButton';
import Typography from '@mui/joy/Typography';
import Link from '@mui/joy/Link';
import { Transition } from 'react-transition-group';
import Modal from '@mui/joy/Modal';
import ModalDialog from '@mui/joy/ModalDialog';
import DialogTitle from '@mui/joy/DialogTitle';
import DialogContent from '@mui/joy/DialogContent';
import { Scrollbar } from 'react-scrollbars-custom';
import List from '@mui/joy/List';
import ListItem from '@mui/joy/ListItem';
import ListItemButton from '@mui/joy/ListItemButton';

// Icons
import GraphicEqIcon from '@mui/icons-material/GraphicEq';
import DarkMode from '@mui/icons-material/DarkMode';
import LightMode from '@mui/icons-material/LightMode';
import Favorite from '@mui/icons-material/Favorite';
import ElectricalServicesIcon from '@mui/icons-material/ElectricalServices';
import VolumeOffIcon from '@mui/icons-material/VolumeOff';
import Settings from '@mui/icons-material/Settings';
import EqualizerIcon from '@mui/icons-material/Equalizer';
import SpeakerIcon from '@mui/icons-material/Speaker';
import InactiveEffect from '@mui/icons-material/FiberManualRecord';
import ActiveEffect from '@mui/icons-material/FiberSmartRecord';
import SaveIcon from '@mui/icons-material/Save';
import ProfilesIcon from '@mui/icons-material/ManageAccounts';
import SaveAsIcon from '@mui/icons-material/Add';
import ResetIcon from '@mui/icons-material/RotateLeft';
import TurnOnIcon from '@mui/icons-material/PowerSettingsNew';
import EqPresetsIcon from '@mui/icons-material/AutoAwesome';
import svg_icon from 'url:assets/icon.svg';
import DurationIcon from '@mui/icons-material/AccessTime';
import ShapeIcon from '@mui/icons-material/Animation';
import NoiseIcon from '@mui/icons-material/Hearing';
import toggleFavorite from './popup_comps/toggleFavorite';
import editProfile from './popup_comps/editProfile';
import addNewProfile from './popup_comps/addNewProfile';

// other
import ModalComponents from './popup_comps/modalComponents';
import { sendMessage } from './sendMessage';

var currentTab: any = {};
Basic.getCurrentTab().then((data) => {
    currentTab = data;
});

// function sendMessage(data: any) {
//     chrome.runtime.sendMessage(data);
// }

// load profile
const profileManager = new ProfileManager();

function StartingModal({ open, setOpen, callback = () => { } }) {

    function buttonAction() {
        callback();
        setOpen(false);
    }

    return (
        <React.Fragment>
            <Transition in={open} timeout={100}>
                {(state: string) => (
                    <Modal
                        keepMounted
                        open={!['exited', 'exiting'].includes(state)}
                        onClose={() => setOpen(false)}
                        slotProps={{
                            backdrop: {
                                sx: {
                                    opacity: 0,
                                    backdropFilter: 'none',
                                    transition: `opacity 200ms, backdrop-filter 200ms`,
                                    ...{
                                        entering: { opacity: 1, backdropFilter: 'blur(8px)' },
                                        entered: { opacity: 1, backdropFilter: 'blur(8px)' },
                                    }[state],
                                },
                            },
                        }}
                        sx={{
                            visibility: state === 'exited' ? 'hidden' : 'visible',
                        }}
                    >
                        <ModalDialog
                            sx={{
                                opacity: 0,
                                transition: `opacity 300ms`,
                                ...{
                                    entering: { opacity: 1 },
                                    entered: { opacity: 1 },
                                }[state],
                            }}
                        >
                            <DialogTitle>
                                <Typography component={'span'} startDecorator={<img
                                    src={svg_icon}
                                    style={{ 'width': '3.1rem', 'height': '3.1rem' }}
                                />} level="body-lg" >
                                    <Typography level="body-lg" noWrap>
                                        {Basic.l('extensionName')}
                                        <Divider sx={{ opacity: 0 }} />
                                        <Typography level="body-sm" noWrap sx={{
                                            opacity: 0.6,
                                            fontSize: "sm",
                                            lineHeight: "xs",
                                            display: "block"
                                        }} >
                                            {Basic.l('extensionTagline')}
                                        </Typography>
                                    </Typography>
                                </Typography>
                            </DialogTitle>
                            <DialogContent>
                                <Button
                                    onClick={buttonAction}
                                    variant="plain"
                                    startDecorator={<TurnOnIcon sx={{
                                        mr: Basic.l('extensionDirection') == 'ltr' ? '0px' : 'none',
                                        ml: Basic.l('extensionDirection') == 'rtl' ? '10px' : 'none',
                                    }} />}
                                    size="lg"
                                    sx={{
                                        border: 1,
                                        borderColor: "transparent",
                                        backgroundColor: "transparent",
                                        '&:hover': {
                                            borderColor: Basic.colors.green,
                                            backgroundColor: "rgba(255, 255, 255, 0.03)",
                                        },
                                        '&:hover svg': {
                                            color: Basic.colors.green,
                                        },
                                        pt: 1,
                                        pb: 1,
                                        borderRadius: "12px"
                                    }}
                                >{Basic.l('start')}</Button>
                            </DialogContent>
                        </ModalDialog>
                    </Modal>
                )}
            </Transition>
        </React.Fragment>
    );
}

var settingsUpdated = false;
var updateSettingsInterval = null;
var tabSettings: any = {
    loaded: false,
    connected: false
};

/* EQ knobs */
var knobs = {
    equalizer: {
        active: true,
        key: 'equalizer',
        name: Basic.l('equalizer'),
        keys: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        names: ['20', '50', '.1k', '.2k', '.5k', '1k', '2k', '4k', '8k', '16k'],
        values: [UserProfile.eq[0], UserProfile.eq[1], UserProfile.eq[2], UserProfile.eq[3], UserProfile.eq[4], UserProfile.eq[5], UserProfile.eq[6], UserProfile.eq[7], UserProfile.eq[8], UserProfile.eq[9]],
        default: [UserProfile.eq[0], UserProfile.eq[1], UserProfile.eq[2], UserProfile.eq[3], UserProfile.eq[4], UserProfile.eq[5], UserProfile.eq[6], UserProfile.eq[7], UserProfile.eq[8], UserProfile.eq[9]],
        event_name: 'change-equalizer',
        min: -40,
        max: 40,
        step: 0.5,
    },
    reverb: {
        active: true,
        key: 'reverb',
        name: Basic.l('reverb'),
        keys: [0, 1, 2, 3, 4],
        names: ['Low', 'High', 'Wet', 'Dry', 'Level'],
        values: [
            UserProfile.tuna_reverb.config.lowCut,
            UserProfile.tuna_reverb.config.highCut,
            UserProfile.tuna_reverb.config.wetLevel,
            UserProfile.tuna_reverb.config.dryLevel,
            UserProfile.tuna_reverb.config.level
        ],
        default: [
            UserProfile.tuna_reverb.config.lowCut,
            UserProfile.tuna_reverb.config.highCut,
            UserProfile.tuna_reverb.config.wetLevel,
            UserProfile.tuna_reverb.config.dryLevel,
            UserProfile.tuna_reverb.config.level
        ],
        event_name: 'change-reverb',
        min: [20, 20, 0, 0, 0],
        max: [22050, 22050, 1, 1, 1],
        step: [2, 1, 0.01, 0.01, 0.01],
    },
    basic_reverb: {
        active: false,
        key: 'basic_reverb',
        name: Basic.l('reverb'),
        keys: [0, 1, 2],
        names: ['Decay', 'Delay', 'Wet'],
        values: [
            UserProfile.tone_reverb.config.decay,
            UserProfile.tone_reverb.config.preDelay,
            UserProfile.tone_reverb.config.wet
        ],
        default: [
            UserProfile.tone_reverb.config.decay,
            UserProfile.tone_reverb.config.preDelay,
            UserProfile.tone_reverb.config.wet
        ],
        event_name: 'change-basic-reverb',
        min: [0, 0, 0],
        max: [2, 1, 0.9],
        step: [0.01, 0.01, 0.01],
    },
    chorus: {
        active: false,
        key: 'chorus',
        name: Basic.l('chorus'),
        keys: [0, 1, 2, 3, 4],
        names: ['Freq', 'Delay', 'Depth', 'Spread', 'Wet'],
        values: [
            UserProfile.chorus.config.frequency,
            UserProfile.chorus.config.delayTime,
            UserProfile.chorus.config.depth,
            UserProfile.chorus.config.spread,
            UserProfile.chorus.config.wet
        ],
        default: [
            UserProfile.chorus.config.frequency,
            UserProfile.chorus.config.delayTime,
            UserProfile.chorus.config.depth,
            UserProfile.chorus.config.spread,
            UserProfile.chorus.config.wet
        ],
        event_name: 'change-chorus',
        min: [-2, -2, -2, 0, 0],
        max: [2, 2, 6.28, 360, 1],
        step: [0.01, 0.01, 0.01, 1, 0.1],
    },
    compressor: {
        active: true,
        key: 'compressor',
        name: Basic.l('compressor'),
        keys: [0, 1, 2, 3, 4, 5],
        names: ['Threshold', 'Attack', 'Release', 'Gain', 'Ratio', 'Knee'],
        values: [
            UserProfile.compressor.config.threshold,
            UserProfile.compressor.config.attack,
            UserProfile.compressor.config.release,
            UserProfile.compressor.config.makeupGain,
            UserProfile.compressor.config.ratio,
            UserProfile.compressor.config.knee
        ],
        default: [
            UserProfile.compressor.config.threshold,
            UserProfile.compressor.config.attack,
            UserProfile.compressor.config.release,
            UserProfile.compressor.config.makeupGain,
            UserProfile.compressor.config.ratio,
            UserProfile.compressor.config.knee
        ],
        event_name: 'change-compressor',
        min: [-60, 0, 0.1, 1, 1, 0],
        max: [0, 1, 1, 10, 20, 40],
        step: [1, 0.1, 0.01, 0.1, 0.1, 1],
    },
    volume: {
        active: true,
        key: 'volume',
        name: Basic.l('volume'),
        value: (UserProfile.volume * 100),
        default: (UserProfile.volume * 100),
        event_name: 'change-volume',
        min: 0,
        max: 800,
        step: 1
    },
    base_volume: {
        active: true,
        key: 'base_volume',
        name: Basic.l('volume'),
        value: (UserProfile.base_volume * 100),
        default: (UserProfile.base_volume * 100),
        event_name: 'change-base-volume',
        min: 0,
        max: 800,
        step: 1
    },
    audio_mode: {
        active: true,
        key: 'audio_mode',
        name: Basic.l('output_mode'),
        value: UserProfile.mono,
        default: UserProfile.mono,
        event_name: 'change-audio-mode'
    },
    spatial_audio: {
        active: false,
        key: 'spatial_audio',
        keys: [0, 1],
        name: Basic.l('immersive_3d'),
        values: [UserProfile.spatial_audio.config.shape, UserProfile.spatial_audio.config.duration],
        default: [UserProfile.spatial_audio.config.shape, UserProfile.spatial_audio.config.duration],
        event_name: 'change-spatial-audio',
        presets: {
            shapes: ["mobius-strip", "infinity", "butterfly", "hummingbird", 
                "triple-knot", "vortex", "spiral", "heart", "rose", "bee", "cube", 
                "trefoil-knot", "celestial-dance",
                "figure-8", "oval", "wiggly", "snake", "helix", "hypotrochoid", "lissajous",
                "flower", "double-helix", "diamond", "concentric-circles",
                "zigzag", "triangle-wave", "ellipse"],
            durations: [5, 10, 15, 20, 25]
        }
    },
    noise: {
        active: false,
        key: 'noise',
        keys: [0, 1],
        name: Basic.l('background_noise'),
        values: [UserProfile.noise.config.type, UserProfile.noise.config.volume],
        default: [UserProfile.noise.config.type, UserProfile.noise.config.volume],
        event_name: 'change-noise',
        min: -90,
        max: -20,
        step: 1,
        presets: {
            types: ["white", "brown", "pink"]
        }
    }
};

function VerticalSlider({ name, id, value, min, max, step, default_value, onSliderChange }) {
    const handleChipClick = () => {
        onSliderChange(id, default_value);
    };

    const handleOneSliderChange = (event: any, newValue: any) => {
        onSliderChange(id, newValue);
    };

    return (
        <div>
            <Box sx={{ mx: 'auto', height: 190 }}>
                <Slider
                    color="primary"
                    variant="solid"
                    max={max}
                    min={min}
                    step={step}
                    value={value}
                    orientation='vertical'
                    aria-label="EQ"
                    valueLabelDisplay="auto"
                    sx={{
                        "--Slider-trackSize": "10px",
                        "--Slider-thumbSize": "16px",
                        "--Slider-thumbWidth": "32px",
                        "--Slider-thumbColor": "#2eff5a !important",
                        "--Slider-thumbBackground": "#2eff5a",
                        "--Slider-valueLabelArrowSize": "10px",
                        pb: 0
                    }}
                    onChange={handleOneSliderChange}
                />
            </Box>
            <Box sx={{ mt: 2, display: 'flex', gap: 1, alignItems: 'center' }}>
                <Chip onClick={handleChipClick} size="sm" variant="plain" style={{ opacity: 0.5 }} startDecorator={<GraphicEqIcon />}>
                    {name}
                </Chip>
            </Box>
        </div>
    );
}

const arraysEqual = (arr1: any[], arr2: any[]): boolean => {
    return arr1.length === arr2.length && arr1.every((value, index) => value === arr2[index]);
};

const EqualizerKnobs = ({ eq_knobs, sliderState, setSliderState, sendStatus, eqStatus, setEqStatus, resetStatus = false }) => {
    // Memoized function to handle slider changes
    const handleSliderChange = useCallback(async (id: number, value: number) => {
        const updatedValues = [...eq_knobs.values];
        updatedValues[id] = value;
        eq_knobs.values = updatedValues; // Update the global values

        // Update the state to trigger a re-render
        setSliderState(updatedValues);
        if (!eqStatus) {
            setEqStatus(true);
            if (!eq_knobs.active) {
                eq_knobs.active = true;
            }
        }

        if (currentTab != null && sendStatus && tabSettings.connected) {
            sendMessage({
                type: eq_knobs.event_name,
                target: 'offscreen',
                tabid: currentTab.id,
                data: updatedValues
            });
        }
    }, [eq_knobs, eqStatus, sendStatus, setSliderState, setEqStatus]);

    // Use useEffect to handle side effects based on eqStatus changes
    useEffect(() => {
        if (eqStatus) {
            const oldKnob = [...knobs[eq_knobs.key].values];
            eq_knobs.values = sliderState;

            if ((sendStatus && !arraysEqual(oldKnob, sliderState) && tabSettings.connected) || (resetStatus && tabSettings.connected)) {
                sendMessage({
                    type: eq_knobs.event_name,
                    target: 'offscreen',
                    tabid: currentTab.id,
                    data: sliderState
                });
            }
        }
    }, [eqStatus, sliderState, eq_knobs, sendStatus, resetStatus]);

    // Function to get knob parameters
    const knobParams = useCallback((params: any, index: number) => {
        return Array.isArray(params) ? params[index] : params;
    }, []);

    // Memoized component for rendering the knobs
    const knobsLoop = useMemo(() => sliderState?.map((value, index) => (
        <Item key={index}>
            <VerticalSlider
                name={eq_knobs.names[index]}
                id={eq_knobs.keys[index]}
                min={knobParams(eq_knobs.min, index)}
                max={knobParams(eq_knobs.max, index)}
                step={knobParams(eq_knobs.step, index)}
                default_value={knobParams(eq_knobs.default, index)}
                value={value}
                onSliderChange={handleSliderChange}
            />
        </Item>
    )), [sliderState, eq_knobs, knobParams, handleSliderChange]);

    return (
        <Box sx={{ width: '100%' }}>
            <Stack
                direction="row"
                justifyContent="space-evenly"
                divider={<Divider orientation="vertical" />}
                spacing={0}
            >
                {knobsLoop}
            </Stack>
        </Box>
    );
};


function VolumeKnob({ eq_knobs, sliderState, setSliderState, isMuted, setMute, mutedAt, setMutedAt, sendStatus }) {

    const toggleMute = () => {
        if (isMuted) {
            setSliderState(mutedAt);
            handleSliderChange(1, mutedAt);
        } else {
            setMute(true);
            setMutedAt(sliderState);
            setSliderState(0);
            handleSliderChange(1, 0);
        }
        setMute(!isMuted);
    };

    // Function to update the global variable and state when a slider changes
    const handleSliderChange = async (id: number, value: number) => {

        const updatedValue = value;
        eq_knobs.value = value; // Update the global values

        // Update the state to trigger a re-render
        setSliderState(updatedValue);

        if (currentTab != null && sendStatus && tabSettings.connected) {
            sendMessage({
                type: eq_knobs.event_name,
                target: 'offscreen',
                tabid: currentTab.id,
                data: {
                    volume: (eq_knobs.value / 100),
                    mute: isMuted,
                    muted_at: (mutedAt / 100)
                }
            });
        }
    };

    eq_knobs.value = sliderState;
    if (sendStatus && tabSettings.connected) {
        sendMessage({
            type: eq_knobs.event_name,
            target: 'offscreen',
            tabid: currentTab.id,
            data: {
                volume: (eq_knobs.value / 100),
                mute: isMuted,
                muted_at: (mutedAt / 100)
            }
        });
    }

    function knobParams(params, index = 0) {
        return Array.isArray(params) ? params[index] : params;
    }

    return (
        <Box sx={{ width: '100%', margin: 0, padding: 0 }}>
            <Grid container spacing={1} sx={{ flexGrow: 1 }}>
                <Grid xs="auto"
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                >
                    <Typography level="body-xs">
                        {eq_knobs.name}
                    </Typography>
                </Grid>
                <Grid xs
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                >
                    <Tooltip title={isMuted ? Basic.l('unmute') : Basic.l('mute')} color="neutral" size="sm" placement="top" variant="soft">
                        <IconButton sx={{ ml: Basic.l('extensionDirection') == 'rtl' ? 1 : 0, mr: Basic.l('extensionDirection') == 'rtl' ? 0 : 1 }} onClick={toggleMute} >
                            <VolumeOffIcon sx={{ color: isMuted ? Basic.colors.green : '' }} />
                        </IconButton>
                    </Tooltip>
                    <Slider
                        color="primary"
                        variant="solid"
                        min={knobParams(eq_knobs.min)}
                        max={knobParams(eq_knobs.max)}
                        step={knobParams(eq_knobs.step)}
                        value={sliderState}
                        orientation='horizontal'
                        aria-label="EQ"
                        valueLabelDisplay="auto"
                        sx={{
                            "--Slider-trackSize": "10px",
                            "--Slider-thumbSize": "24px",
                            "--Slider-thumbWidth": "24px",
                            "--Slider-thumbColor": "#2eff5a !important",
                            "--Slider-thumbBackground": "#2eff5a",
                            "--Slider-valueLabelArrowSize": "10px",
                            pb: 0
                        }}
                        onChange={handleSliderChange}
                    />
                </Grid>
            </Grid>

        </Box>
    );
}

async function startEQ(callback: any = (settings: any = null) => { }) {

    let profile = await profileManager.getCurrentProfile();
    settingsUpdated = false;
    updateSettingsInterval = setInterval(() => {
        if (settingsUpdated) {
            callback(tabSettings);
            clearInterval(updateSettingsInterval);
        }
    }, 10);
    sendMessage({
        type: 'init-equalizer',
        target: 'background',
        current_tab: { ...currentTab, ...{ profile: profile } }
    });
}

var check_connectivity: any = null;
function StartButton({ connected, setConnected, toggle_callback, connected_callback }) {
    const [mounted, setMounted] = useState(false);
    const [open, setOpen] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(false);
    const [startButton, setButtonStatus] = useState(Basic.l('start'));

    function updateButton(connected) {
        setConnected(connected);
        if (connected) {
            setButtonStatus(Basic.l('stop'));
        } else {
            setButtonStatus(Basic.l('start'));
        }
    }

    // necessary for server-side rendering
    // because mode is undefined on the server
    React.useEffect(() => {
        setMounted(true);

        check_connectivity = setInterval(() => {
            if (tabSettings.loaded) {
                updateButton(tabSettings.connected ? true : false);
                connected_callback(tabSettings);
                setOpen(!tabSettings.connected);
                clearInterval(check_connectivity);
            }
        }, 10);
    }, []);

    if (!mounted) {
        return null;
    }

    function startAction() {
        setLoading(true);
        startEQ((newSettings) => {
            setLoading(false);
            updateButton(newSettings.connected ? true : false);
            connected_callback(newSettings);
            if (!newSettings.connected) {
                setOpen(true);
            }
        });
        setTimeout(() => {
            toggle_callback();
        }, connected ? 100 : 500);
    }

    function toggleAction() {
        setLoading(true);
        startEQ((newSettings) => {
            setLoading(false);
            updateButton(newSettings.connected ? true : false);
            connected_callback(newSettings);
            if (!newSettings.connected) {
                setOpen(true);
            }
        });
        setTimeout(() => {
            toggle_callback();
        }, connected ? 100 : 500);
    }

    return (
        <div>
            <Button loading={loading} variant="soft" color="neutral" onClick={toggleAction} sx={{
                width: "80px",
                height: "80px",
                m: 0,
                p: 0,
                cursor: 'pointer',
                justifyContent: 'center',
                alignItems: 'center',
                border: 1,
                borderColor: "transparent",
                '&:hover': {
                    // borderColor: Basic.colors.green
                },
                '&:hover svg': {
                    color: loading ? '' : Basic.colors.green,
                },
            }}>
                <Stack spacing={1} sx={{
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                    <ElectricalServicesIcon sx={{
                        width: "35px",
                        height: "35px",
                        color: (connected && !loading) ? Basic.colors.green : '',
                        transform: Basic.l('extensionDirection') == 'rtl' ? 'scalex(-1)' : ''
                    }} />
                    <Typography level="body-xs" >
                        {startButton}
                    </Typography>
                </Stack>
            </Button>

            <StartingModal open={open} setOpen={setOpen} callback={startAction} />
        </div>
    );
}

function loadTabSettings(settings: any) {
    if (typeof settings != 'undefined' && settings != null) {
        tabSettings = { ...settings, ...{ loaded: true } };
    } else {
        tabSettings = { connected: false, loaded: true };
    }
    settingsUpdated = true;
}

var port: any;
function connectBackground(ele: any = null) {

    if (port) {
        port.disconnect();
    }

    // Connect to the background script
    port = chrome.runtime.connect({ name: "popupConnection" });

    // Use the chrome.tabs API to get the current tab information
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
            // Get the tabId of the current tab
            const currentTabId = tabs[0].id;

            // Send the currentTabId to the background script
            port.postMessage({ type: 'tabId', tabId: currentTabId });

            // load tab settings
            //port.postMessage({ type: 'tabSettings', tabId: currentTabId });
        }
    });

    // Listen for messages from the background script
    port.onMessage.addListener((message: any) => {
        switch (message.type) {
            case 'audioData':
                if (ele != null) {
                    Visualizer.update(
                        currentTab.id,
                        ele,
                        message.frequencyData,
                        message.frequencyBinCount
                    );
                }
                break;
            case 'loadTabSettings':
                loadTabSettings(message.settings);
                break;
        }
    });
}

var profile_port: any;
function requestProfile(callback = (settings: any) => { }) {

    if (profile_port) {
        profile_port.disconnect();
    }

    // Connect to the background script
    profile_port = chrome.runtime.connect({ name: "requestingProfile" });

    // Use the chrome.tabs API to get the current tab information
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
            // Get the tabId of the current tab
            const currentTabId = tabs[0].id;

            // Send the currentTabId to the background script
            profile_port.postMessage({ type: 'tabId', tabId: currentTabId });

        }
    });

    // Listen for messages from the background script
    profile_port.onMessage.addListener((message: any) => {
        switch (message.type) {
            case 'respondingProfile':
                callback(message.settings);
                break;
        }
    });
}

// close ports before closing the window
window.addEventListener('beforeunload', () => {
    if (port) {
        port.disconnect();
    }
    if (profile_port) {
        profile_port.disconnect();
    }
});

function ThemeModeSwitcher() {
    const { mode, setMode } = useColorScheme();
    useEffect(() => {
        setMode(mode === 'light' ? 'light' : 'dark');
    });

    return (
        <Link
            onClick={() => {
                setMode(mode === 'light' ? 'dark' : 'light');
            }}
            href="#"
            level="body-xs"
            underline="none"
            startDecorator={mode === 'light' ? <DarkMode /> : <LightMode />}
            sx={{
                fontWeight: 'md',
                mr: Basic.l('extensionDirection') == 'rtl' ? 'auto' : 'none',
                ml: Basic.l('extensionDirection') == 'ltr' ? 'auto' : 'none',
                color: 'text.secondary',
                '&:hover': { color: mode === 'dark' ? Basic.colors.green : '#000000' },
                minWidth: '80px',
                direction: Basic.l('extensionDirection') == 'ltr' ? 'rtl' : 'ltr'
            }}
        >
            {mode === 'light' ? Basic.l('dark') : Basic.l('light')}
        </Link>
    );
}


function SpatialAudioArea({ eq_knobs, sendStatus, resetStatus, spatialAudioStatus, setSpatialAudioStatus, spatialAudio, setSpatialAudio }) {
    const arraysEqual = async (arr1: any, arr2: any) => {
        return arr1.length === arr2.length && arr1.every((value, index) => value === arr2[index]);
    }

    const handleShapeChange = async (shape: string) => {
        handleChange(0, shape);
    }
    const handleDurationChange = async (duration: number) => {
        handleChange(1, duration);
    }

    // Function to update the global variable and state when a slider changes
    const handleChange = async (id: number, value: number | string) => {

        const updatedValues = [...eq_knobs.values];
        updatedValues[id] = value;
        eq_knobs.values = updatedValues; // Update the global values

        // Update the state to trigger a re-render
        setSpatialAudio(updatedValues);
        if (!spatialAudioStatus) {
            setSpatialAudioStatus(true);
            if (!eq_knobs.active) {
                eq_knobs.active = true;
            }
        }

        if (currentTab != null && sendStatus) {
            sendMessage({
                type: eq_knobs.event_name,
                target: 'offscreen',
                tabid: currentTab.id,
                data: [
                    updatedValues[0],
                    (updatedValues[1] * 1000)
                ]
            });
        }
    };


    if (spatialAudioStatus) {
        var oldKnob = [...knobs[eq_knobs.key].values];
        eq_knobs.values = spatialAudio

        if ((sendStatus && !arraysEqual(oldKnob, spatialAudio)) || resetStatus) {
            sendMessage({
                type: eq_knobs.event_name,
                target: 'offscreen',
                tabid: currentTab.id,
                data: [
                    spatialAudio[0],
                    (spatialAudio[1] * 1000)
                ]
            });
        }
    }
    return (
        <Grid container spacing={0} sx={{ flexGrow: 1 }}>
            <Grid xs="auto" sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }} >
                <Box sx={{ px: 1 }}>
                    <List sx={{ display: 'grid', gridTemplateColumns: 'repeat(1, 1fr)', gap: 1.49 }}>
                        {knobs.spatial_audio.presets.durations.map((duration) => (
                            <ListItem
                                onClick={() => {
                                    handleDurationChange(duration);
                                }}
                                key={duration}
                            >
                                <ListItemButton sx={{
                                    borderRadius: '7px',
                                    py: 0,
                                    backgroundColor: spatialAudio[1] == duration ? 'background.level1' : 'transparent',
                                    '&:hover .plugIcon': {
                                        color: Basic.colors.green,
                                    },
                                    border: 0,
                                    borderLeft: Basic.l('extensionDirection') == 'rtl' ? 'none' : '2px solid',
                                    borderRight: Basic.l('extensionDirection') == 'rtl' ? '2px solid' : 'none',
                                    borderColor: spatialAudio[1] == duration ? Basic.colors.green : 'transparent'
                                }}>
                                    <Typography level="body-xs" startDecorator={<DurationIcon sx={{ color: spatialAudio[1] == duration ? Basic.colors.green : '' }} className="plugIcon" />} noWrap sx={{ width: '100%', fontSize: 'xs', fontWeight: 'md', py: 0 }}>
                                        {duration + 's'}
                                    </Typography>

                                </ListItemButton>
                            </ListItem>
                        ))}
                    </List>

                </Box>
            </Grid>
            <Grid sx={{ mr: Basic.l('extensionDirection') == 'rtl' ? '' : 1, ml: Basic.l('extensionDirection') == 'rtl' ? 1 : '' }} xs>
                <Scrollbar>
                    <Box sx={{ px: 1 }}>
                        <List size="sm" sx={{
                            display: 'grid',
                            gridTemplateColumns: 'repeat(3, 1fr)',
                            gap: 0.5,
                            '--ListItem-paddingY': '0px'
                        }} >
                            {knobs.spatial_audio.presets.shapes.map((shape) => (
                                <ListItem
                                    onClick={() => {
                                        handleShapeChange(shape);
                                    }}
                                    key={shape}
                                >
                                    <ListItemButton sx={{
                                        borderRadius: '7px',
                                        py: 0,
                                        backgroundColor: spatialAudio[0] == shape ? 'background.level1' : 'transparent',
                                        '&:hover .plugIcon': {
                                            color: Basic.colors.green,
                                        },
                                        border: 0,
                                        borderLeft: Basic.l('extensionDirection') == 'rtl' ? 'none' : '2px solid',
                                        borderRight: Basic.l('extensionDirection') == 'rtl' ? '2px solid' : 'none',
                                        borderColor: spatialAudio[0] == shape ? Basic.colors.green : 'transparent'
                                    }}>
                                        <Typography level="body-xs" startDecorator={<ShapeIcon sx={{ color: spatialAudio[0] == shape ? Basic.colors.green : '' }} className="plugIcon" />} noWrap sx={{ width: '100%', fontSize: 'xs', fontWeight: 'md', py: 0 }}>
                                            {shape.charAt(0).toUpperCase() + shape.slice(1)}
                                        </Typography>

                                    </ListItemButton>
                                </ListItem>
                            ))}
                        </List>

                    </Box>
                </Scrollbar>
            </Grid>
        </Grid>
    );
}

var checkTabSettings: any;
var globalTabDomain: string = null;
export default function EqualizerDash() {
    // mount once
    const [hasRendered, setHasRendered] = useState<boolean>(false);
    const [tabIndex, setTabIndex] = React.useState(0);

    // preventing the initial send to background 
    const [sendStatus, setSendStatus] = useState<boolean>(false);
    const [resetStatus, setResetStatus] = useState<boolean>(false);
    const [opacity, setOpacity] = useState(0);
    const [profilesModalStatus, setProfilesModalStatus] = useState<boolean>(false);
    const [settingsModal, openSettingsModal] = useState<boolean>(false);
    const [profile, setProfile] = useState(UserProfile);
    const [profilesList, setProfilesList] = useState([]);

    const visualizer_area = useRef(null);

    // popup
    const [tabTitle, setTabTitle] = useState<string>(currentTab.title ? currentTab.title : Basic.l('extensionName'));
    const [tabIcon, setTabIcon] = useState<string>(currentTab.favIconUrl ? currentTab.favIconUrl : svg_icon);
    const [tabConnected, setTabConnected] = useState<boolean>(tabSettings.connected ? true : false);

    const [baseVolumeModal, openBaseVolumeModal] = useState<boolean>(false);

    // volume
    const [baseVolume, setBaseVolume] = useState<number>(knobs.base_volume.value);
    const [mainVolume, setMainVolume] = useState<number>(knobs.volume.value);
    const [isMuted, setMute] = useState<boolean>(false);
    const [mutedAt, setMutedAt] = useState<number>(100);

    // maudiio mode mono (true) / stereo (false)
    const [audioModeModal, openAudioModeModal] = useState<boolean>(false);
    const [audioMode, setAudioMode] = useState<boolean>(false);

    // knobs
    const [equalizerPresetsModal, openEqualizerPresetsModal] = useState<boolean>(false);
    const [equalizerStatus, setEqualizerStatus] = useState<boolean>(knobs.equalizer.active);
    const [equalizerKnobs, setEqualizerKnobs] = useState(knobs.equalizer.values);

    const [reverbStatus, setReverbStatus] = useState<boolean>(knobs.reverb.active);
    const [reverbKnobs, setReverbKnobs] = useState(knobs.reverb.values);

    const [basicreverbStatus, setBasicreverbStatus] = useState<boolean>(knobs.basic_reverb.active);
    const [basicreverbKnobs, setBasicreverbKnobs] = useState(knobs.basic_reverb.values);

    const [chorusStatus, setChorusStatus] = useState<boolean>(knobs.chorus.active);
    const [chorusKnobs, setChorusKnobs] = useState(knobs.chorus.values);

    const [compressorStatus, setCompresserStatus] = useState<boolean>(knobs.compressor.active);
    const [compressorKnobs, setCompresserKnobs] = useState(knobs.compressor.values);

    const [spatialAudioStatus, setSpatialAudioStatus] = useState<boolean>(knobs.spatial_audio.active);
    const [spatialAudio, setSpatialAudio] = useState(knobs.spatial_audio.values);

    const [noiseModal, openNoiseModal] = useState<boolean>(false);
    const [noiseStatus, setNoiseStatus] = useState<boolean>(knobs.noise.active);
    const [noise, setNoise] = useState(knobs.noise.values);

    async function captureExperience(event_path: string, event_name: string, event_object: any) {
        const appExperience = await import('./appExperience');
        appExperience.default(event_path, event_name, event_object);
    }

    async function updateTabInfo(tabData: any) {
        if (tabData.title && tabData.title != '') {
            setTabTitle(tabData.title);
        } else {
            setTabTitle(Basic.l('extensionName'));
        }

        if (tabData.favIconUrl) {
            setTabIcon(tabData.favIconUrl);
        } else {
            setTabIcon(svg_icon);
        }

        if (tabData.url) {
            const tabUrl = new URL(tabData.url);
            globalTabDomain = tabUrl.hostname;
        } else {
            globalTabDomain = 'local';
        }
    }

    async function toggleEffect(name: string, effectStatus: any, setEffectStatus: any, callback: any) {
        var currentEffect = knobs[name];
        if (effectStatus) {
            sendMessage({
                type: 'stop-effect',
                target: 'offscreen',
                tabid: currentTab.id,
                data: currentEffect.key
            });

            setEffectStatus(false);
            currentEffect.active = false;

        } else {
            setEffectStatus(true);
            currentEffect.active = true;

            setResetStatus(true);
            callback(knobs[name].values);
            setTimeout(() => {
                setResetStatus(false);
            }, 50);

        }
    }

    async function resetEffect(name: string, callback: any) {
        setResetStatus(true);
        callback(knobs[name].default);
        setTimeout(() => {
            setResetStatus(false);
        }, 50);
    }

    const [effectPresetsModal, openEffectPresetsModal] = useState(false);
    const [effectPresets, setEffectPresets] = useState({
        presets: [],
        current: [],
        callback: async () => { }
    });

    function EffectSidebar({ effectStatus, setEffectStatus, effectKnobs, effect, name, reset }) {
        return (
            <Card variant="soft" sx={{ justifyContent: 'top', py: 0, px: 0 }} >
                <ButtonGroup spacing="0.5rem" orientation="vertical" variant="plain">
                    <Tooltip title={effectStatus ? Basic.l('deactivate') + ' ' + Basic.l(name).toLocaleLowerCase() : Basic.l('activate') + ' ' + Basic.l(name).toLocaleLowerCase()} color="neutral" size="sm" placement={Basic.l('extensionDirection') == 'rtl' ? 'right' : 'left'} variant="soft">
                        <IconButton onClick={() => toggleEffect(effect, effectStatus, setEffectStatus, reset)} size="sm" variant="plain" sx={{ shadow: 0 }} >
                            <TurnOnIcon sx={{ color: effectStatus ? Basic.colors.green : '' }} />
                        </IconButton>
                    </Tooltip>
                    <Tooltip title={Basic.l('instant_presets')} color="neutral" size="sm" placement={Basic.l('extensionDirection') == 'rtl' ? 'right' : 'left'} variant="soft">
                        <IconButton onClick={() => {
                            if (!effectStatus) {
                                setEffectStatus(true);
                            }
                            setEffectPresets({
                                presets: Presets[effect],
                                current: effectKnobs,
                                callback: reset
                            });
                            openEffectPresetsModal(true);
                        }} size="sm" variant="plain" >
                            <EqPresetsIcon />
                        </IconButton>
                    </Tooltip>
                    <Tooltip title={Basic.l('reset') + ' ' + Basic.l(name).toLocaleLowerCase()} color="neutral" size="sm" placement={Basic.l('extensionDirection') == 'rtl' ? 'right' : 'left'} variant="soft">
                        <IconButton onClick={() => resetEffect(effect, reset)} size="sm" variant="plain" >
                            <ResetIcon />
                        </IconButton>
                    </Tooltip>
                </ButtonGroup>
            </Card>
        );
    }

    useEffect(() => {
        document.title = Basic.l('extensionName');
        document.dir = ['rtl', 'ltr'].includes(Basic.l('extensionDirection')) ? Basic.l('extensionDirection') : 'ltr';

        Visualizer.update(
            currentTab.id,
            visualizer_area.current,
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            128
        );
        connectBackground(visualizer_area.current);


        setInterval(function () {
            Basic.getCurrentTab().then((data: any) => {
                updateTabInfo(data);
            });
        }, 1000);

        var beforeStats = [false, false];

        if (!hasRendered) {
            setHasRendered(true);
            checkTabSettings = setInterval(() => {
                if (tabSettings.loaded) {
                    if (tabSettings.connected || !tabSettings.connected) {
                        updateLoadedSettings(tabSettings);
                        setOpacity(1);
                        setSendStatus(true);
                    }
                    beforeStats[0] = true;
                    clearInterval(checkTabSettings);
                }
            }, 10);
        }

        // fallback that enables opacity
        setTimeout(() => {
            if (opacity == 0) {
                setOpacity(1);
            }
        }, 300);

        // load profiles
        var loadedProfilesList: any;
        (async (beforeStats) => {
            loadedProfilesList = await profileManager.listProfiles();
            setProfilesList(loadedProfilesList);
            beforeStats[1] = true;
        })(beforeStats);

        let checkBeforeStats = setInterval(async () => {
            if (beforeStats.every(stat => stat === true) && globalTabDomain != null) {
                clearInterval(checkBeforeStats);

                let membershipStats = await Basic.getMembershipType();

                // send stats 
                captureExperience('/popup', 'popup_open', {
                    profiles_count: loadedProfilesList ? Object.keys(loadedProfilesList).length : 0,
                    tab_domain: globalTabDomain,
                    tab_connected: tabSettings.connected ? "connected" : "disconnected",
                    equalizer: "on",
                    noise: tabSettings.noise.status ? "on" : "off",
                    mono: tabSettings.mono ? "on" : "off",
                    pro_reverb: tabSettings.tuna_reverb.status ? "on" : "off",
                    basic_reverb: tabSettings.tone_reverb.status ? "on" : "off",
                    chorus: tabSettings.chorus.status ? "on" : "off",
                    spatial_audio: tabSettings.spatial_audio.status ? "on" : "off",
                    compressor: tabSettings.compressor.status ? "on" : "off",
                    user_type: membershipStats
                });
            }
        }, 100);

        // clear stats anyway
        setTimeout(() => {
            if (checkBeforeStats) {
                clearInterval(checkBeforeStats);
            }
        }, 3000);

    }, []);

    function updateLoadedSettings(settings: any, noSend: boolean = false) {
        if (typeof settings != 'undefined' && settings != null) {

            if (noSend) {
                setSendStatus(false);
            }

            setProfile(settings);

            setEqualizerKnobs(settings.eq);

            setReverbStatus(settings.tuna_reverb.status ? true : false);
            knobs.reverb.active = settings.tuna_reverb.status;
            setReverbKnobs([
                settings.tuna_reverb.config.lowCut,
                settings.tuna_reverb.config.highCut,
                settings.tuna_reverb.config.wetLevel,
                settings.tuna_reverb.config.dryLevel,
                settings.tuna_reverb.config.level
            ]);
            knobs.reverb.values = [
                settings.tuna_reverb.config.lowCut,
                settings.tuna_reverb.config.highCut,
                settings.tuna_reverb.config.wetLevel,
                settings.tuna_reverb.config.dryLevel,
                settings.tuna_reverb.config.level
            ];

            setBasicreverbStatus(settings.tone_reverb.status ? true : false);
            knobs.basic_reverb.active = settings.tone_reverb.status;
            setBasicreverbKnobs([
                settings.tone_reverb.config.decay,
                settings.tone_reverb.config.preDelay,
                settings.tone_reverb.config.wet
            ]);
            knobs.basic_reverb.values = [
                settings.tone_reverb.config.decay,
                settings.tone_reverb.config.preDelay,
                settings.tone_reverb.config.wet
            ];

            setChorusStatus(settings.chorus.status ? true : false);
            knobs.chorus.active = settings.chorus.status;
            setChorusKnobs([
                settings.chorus.config.frequency,
                settings.chorus.config.delayTime,
                settings.chorus.config.depth,
                settings.chorus.config.spread,
                settings.chorus.config.wet
            ]);
            knobs.chorus.values = [
                settings.chorus.config.frequency,
                settings.chorus.config.delayTime,
                settings.chorus.config.depth,
                settings.chorus.config.spread,
                settings.chorus.config.wet
            ];

            setCompresserStatus(settings.compressor.status ? true : false);
            knobs.compressor.active = settings.compressor.status;
            setCompresserKnobs([
                settings.compressor.config.threshold,
                settings.compressor.config.attack,
                settings.compressor.config.release,
                settings.compressor.config.makeupGain,
                settings.compressor.config.ratio,
                settings.compressor.config.knee
            ]);
            knobs.compressor.values = [
                settings.compressor.config.threshold,
                settings.compressor.config.attack,
                settings.compressor.config.release,
                settings.compressor.config.makeupGain,
                settings.compressor.config.ratio,
                settings.compressor.config.knee
            ];

            setSpatialAudioStatus(settings.spatial_audio.status ? true : false);
            knobs.spatial_audio.active = settings.spatial_audio.status;
            setSpatialAudio([
                settings.spatial_audio.config.shape,
                (settings.spatial_audio.config.duration / 1000)
            ]);
            knobs.spatial_audio.values = [
                settings.spatial_audio.config.shape,
                (settings.spatial_audio.config.duration / 1000)
            ];

            setNoiseStatus(settings.noise.status ? true : false);
            knobs.noise.active = settings.noise.status;
            setNoise([
                settings.noise.config.type,
                settings.noise.config.volume
            ]);
            knobs.noise.values = [
                settings.noise.config.type,
                settings.noise.config.volume
            ];

            setMainVolume((settings.volume * 100));
            setMute(settings.mute ? true : false);
            setMutedAt((settings.muted_at * 100));

            setBaseVolume((settings.base_volume * 100));
            setAudioMode(settings.mono);

            if (noSend) {
                setTimeout(() => {
                    setSendStatus(true);
                }, 10);
            }
        }
    }

    function connectedCallback(instanceSettings: any) {
        // setTabConnected(true);

        const beforeTabStats = tabConnected ? true : false;

        setTabConnected(instanceSettings?.connected ? true : false);
        tabSettings = instanceSettings;
        setProfile(instanceSettings);
        if (tabSettings?.connected) {
            updateLoadedSettings(instanceSettings);

            (async () => {
                if(beforeTabStats == false){
                    let membershipStats = await Basic.getMembershipType();
    
                    captureExperience('/popup', 'equalizer_start', {
                        profiles_count: profilesList ? Object.keys(profilesList).length : 0,
                        tab_domain: globalTabDomain,
                        tab_connected: tabSettings.connected ? "connected" : "disconnected",
                        equalizer: "on",
                        noise: tabSettings.noise.status ? "on" : "off",
                        mono: tabSettings.mono ? "on" : "off",
                        pro_reverb: tabSettings.tuna_reverb.status ? "on" : "off",
                        basic_reverb: tabSettings.tone_reverb.status ? "on" : "off",
                        chorus: tabSettings.chorus.status ? "on" : "off",
                        spatial_audio: tabSettings.spatial_audio.status ? "on" : "off",
                        compressor: tabSettings.compressor.status ? "on" : "off",
                        user_type: membershipStats
                    });
                }
            })();
        }

    }

    function reconnectBackground() {
        connectBackground(visualizer_area.current);
        Visualizer.update(
            currentTab.id,
            visualizer_area.current,
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            128
        );
    }

    return (
        <CssVarsProvider defaultMode="system" theme={theme} >
            <div dir={Basic.l('extensionDirection')} style={{ margin: "0px", padding: "0px", width: "580px" }} className="main_popup no-select">
                <Sheet id="popup_wrapper">

                    <Card
                        variant="plain"
                        sx={{
                            width: "calc(100% - 20px)",
                            bgcolor: 'initial',
                            p: "10px",
                            borderRadius: "7px",
                        }}
                    >
                        <Box sx={{ position: 'relative' }}>
                            <AspectRatio sx={{
                                borderRadius: "6px"
                            }} ratio="16/4.2">
                                <figure>
                                    <div style={{ margin: "0px", padding: "0px", width: "100%", height: "100%" }} ref={visualizer_area} id="visualizer"></div>
                                </figure>
                            </AspectRatio>
                            <CardCover
                                className="gradient-cover"
                                sx={{
                                    '&:hover, &:focus-within': {
                                        opacity: 1,
                                    },
                                    opacity: 0,
                                    transition: '0.05s ease-in',
                                    background:
                                        'linear-gradient(180deg, transparent 62%, rgba(0,0,0,0.00345888) 63.94%, rgba(0,0,0,0.014204) 65.89%, rgba(0,0,0,0.0326639) 67.83%, rgba(0,0,0,0.0589645) 69.78%, rgba(0,0,0,0.0927099) 71.72%, rgba(0,0,0,0.132754) 73.67%, rgba(0,0,0,0.177076) 75.61%, rgba(0,0,0,0.222924) 77.56%, rgba(0,0,0,0.267246) 79.5%, rgba(0,0,0,0.30729) 81.44%, rgba(0,0,0,0.341035) 83.39%, rgba(0,0,0,0.367336) 85.33%, rgba(0,0,0,0.385796) 87.28%, rgba(0,0,0,0.396541) 89.22%, rgba(0,0,0,0.4) 91.17%)',
                                }}
                            >
                                {/* The first box acts as a container that inherits style from the CardCover */}
                                <div>
                                    <Box
                                        sx={{
                                            p: 2,
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: 1.5,
                                            flexGrow: 1,
                                            alignSelf: 'flex-end',
                                        }}
                                    >
                                        <Typography level="h2" noWrap sx={{ fontSize: 'lg' }}>
                                            <Link
                                                href="#"
                                                overlay
                                                underline="none"
                                                sx={{
                                                    color: '#fff',
                                                    textOverflow: 'ellipsis',
                                                    overflow: 'hidden',
                                                    display: 'block',
                                                }}
                                            >
                                                {profilesList[profileManager.selectActiveProfileId()]?.name}
                                            </Link>
                                        </Typography>
                                        <Tooltip title={Basic.l('add_profile')} color="neutral" size="sm" placement="top" variant="soft">
                                            <IconButton
                                                onClick={() => {
                                                    addNewProfile(profile, setProfilesList, true, profileManager, requestProfile, tabSettings);
                                                }}
                                                size="sm"
                                                variant="solid"
                                                color="neutral"
                                                sx={{
                                                    mr: Basic.l('extensionDirection') == 'rtl' ? 'auto' : 'none',
                                                    ml: Basic.l('extensionDirection') == 'ltr' ? 'auto' : 'none',
                                                    bgcolor: 'rgba(0 0 0 / 0.2)'
                                                }}
                                            >
                                                <SaveAsIcon />
                                            </IconButton>
                                        </Tooltip>
                                        <Tooltip title={Basic.l('save_profile')} color="neutral" size="sm" placement="top" variant="soft">
                                            <IconButton
                                                onClick={async () => {
                                                    editProfile(await profileManager.getCurrentProfileId(), profilesList, setProfilesList, updateLoadedSettings, profile, profileManager, tabSettings, requestProfile);
                                                }}
                                                size="sm"
                                                variant="solid"
                                                color="neutral"
                                                sx={{ bgcolor: 'rgba(0 0 0 / 0.2)' }}
                                            >
                                                <SaveIcon />
                                            </IconButton>
                                        </Tooltip>
                                        <Tooltip title={Basic.l('favorite_profile')} color="neutral" size="sm" placement="top" variant="soft">
                                            <IconButton
                                                onClick={async () => {
                                                    toggleFavorite(await profileManager.getCurrentProfileId(), profilesList, setProfilesList, profileManager);
                                                }}
                                                size="sm"
                                                variant="solid"
                                                color="neutral"
                                                sx={{ bgcolor: 'rgba(0 0 0 / 0.2)' }}
                                            >
                                                <Favorite
                                                    sx={{
                                                        color: profilesList[profileManager.selectActiveProfileId()]?.favorite ? Basic.colors.red : ''
                                                    }} />
                                            </IconButton>
                                        </Tooltip>
                                        <Tooltip title={Basic.l('switch_profile')} color="neutral" size="sm" placement="top" variant="soft">
                                            <IconButton
                                                onClick={() => { setProfilesModalStatus(true) }}
                                                size="sm"
                                                variant="solid"
                                                color="neutral"
                                                sx={{ bgcolor: 'rgba(0 0 0 / 0.2)' }}
                                            >
                                                <ProfilesIcon />
                                            </IconButton>
                                        </Tooltip>
                                    </Box>
                                </div>
                            </CardCover>
                        </Box>
                        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                            <img
                                src={tabIcon}
                                style={{ borderRadius: '2px', 'width': '1.2rem', 'height': '1.2rem' }}
                            />
                            <Typography noWrap sx={{ fontSize: 'sm', fontWeight: 'md' }}>
                                {tabTitle}
                            </Typography>
                            <Chip
                                onClick={() => openBaseVolumeModal(true)}
                                variant="outlined"
                                color="neutral"
                                size="sm"
                                sx={{
                                    borderRadius: 'sm',
                                    py: 0.1,
                                    px: 0.6,
                                    cursor: 'pointer'
                                }}
                                startDecorator={<ElectricalServicesIcon sx={{ transform: Basic.l('extensionDirection') == 'rtl' ? 'scalex(-1)' : '', color: tabConnected ? Basic.colors.green : '' }} />}
                            >
                                {tabConnected ? Basic.l('connected').toLowerCase() : Basic.l('disconnected').toLowerCase()}
                            </Chip>
                            <ThemeModeSwitcher />
                        </Box>
                    </Card>
                    <Sheet sx={{ filter: opacity == 0 ? 'blur(10px)' : 'none', m: 0, p: 0 }}>
                        <Grid container spacing={1} sx={{ mx: 1, flexGrow: 1 }}>
                            <Grid xs="auto">
                                <Box sx={{ justifyContent: 'center' }} >
                                    <StartButton connected={tabConnected} setConnected={setTabConnected} connected_callback={connectedCallback} toggle_callback={reconnectBackground} />
                                </Box>
                            </Grid>
                            <Grid xs>
                                <Card variant="soft" sx={{ justifyContent: 'center', py: 0, px: 2, borderRadius: '7px' }} >
                                    <VolumeKnob
                                        sliderState={mainVolume} setSliderState={setMainVolume}
                                        isMuted={isMuted} setMute={setMute}
                                        mutedAt={mutedAt} setMutedAt={setMutedAt}
                                        eq_knobs={knobs.volume}
                                        sendStatus={sendStatus} />
                                </Card>
                                <Card variant="soft" sx={{ justifyContent: 'center', mt: 1, py: 0, px: 0 }} >
                                    <Sheet>
                                        <ButtonGroup
                                            variant="soft"
                                            buttonFlex={1}
                                            aria-label="control buttons: equalizer presets, profiles, settings"
                                            sx={{
                                                direction: 'ltr',
                                                p: 0,
                                                width: 500,
                                                maxWidth: '100%',
                                                overflow: 'auto',
                                            }} >
                                            <Button onClick={() => { setTabIndex(0); openEqualizerPresetsModal(true); }}>
                                                <Typography level="body-xs" startDecorator={<EqualizerIcon />}>{Basic.l('eq_presets')}</Typography>
                                            </Button>
                                            <Button onClick={() => setProfilesModalStatus(true)}>
                                                <Typography level="body-xs" startDecorator={<ProfilesIcon />}>{Basic.l('profiles')}</Typography>
                                            </Button>
                                            <Button onClick={() => openSettingsModal(true)} >
                                                <Typography level="body-xs" startDecorator={<Settings />}>{Basic.l('settings')}</Typography>
                                            </Button>
                                            <Tooltip title={Basic.l('background_noise')} color="neutral" size="sm" placement="top" variant="soft">
                                                <IconButton onClick={() => openNoiseModal(true)}>
                                                    <NoiseIcon sx={{ color: noiseStatus ? Basic.colors.green : '' }} />
                                                </IconButton>
                                            </Tooltip>
                                            <Tooltip title={Basic.l('output_mode')} color="neutral" size="sm" placement="top" variant="soft">
                                                <IconButton onClick={() => { openAudioModeModal(true) }}>
                                                    <SpeakerIcon />
                                                </IconButton>
                                            </Tooltip>
                                        </ButtonGroup>
                                    </Sheet>
                                </Card>
                            </Grid>
                        </Grid>
                        <Tabs value={tabIndex} onChange={(event, value) => setTabIndex(value as number)} size="sm">
                            <TabList sx={{
                                pt: 1,
                                justifyContent: 'center',
                                [`&& .${tabClasses.root}`]: {
                                    flex: 'initial',
                                    bgcolor: 'transparent',
                                    '&:hover': {
                                        bgcolor: 'transparent',
                                    },
                                    '&:focus': {
                                        bgcolor: 'transparent',
                                    },
                                    [`&.${tabClasses.selected}`]: {
                                        color: 'primary.plainColor',
                                        '&::after': {
                                            height: 2,
                                            borderTopLeftRadius: 3,
                                            borderTopRightRadius: 3,
                                            bgcolor: 'primary.500',
                                        },
                                    },
                                },
                            }} >
                                <Tab variant="plain" color="primary" indicatorInset>
                                    <Typography level="body-sm" startDecorator={equalizerStatus ? <ActiveEffect sx={{ color: Basic.colors.green }} fontSize='inherit' /> : <InactiveEffect fontSize='inherit' />}>
                                        {knobs.equalizer.name}
                                    </Typography>
                                </Tab>
                                <Tab variant="plain" color="primary" indicatorInset>
                                    <Typography level="body-sm" startDecorator={reverbStatus || basicreverbStatus ? <ActiveEffect sx={{ color: Basic.colors.green }} fontSize='inherit' /> : <InactiveEffect fontSize='inherit' />}>
                                        {knobs.reverb.name}
                                    </Typography>
                                </Tab>
                                <Tab variant="plain" color="primary" indicatorInset>
                                    <Typography level="body-sm" startDecorator={chorusStatus ? <ActiveEffect sx={{ color: Basic.colors.green }} fontSize='inherit' /> : <InactiveEffect fontSize='inherit' />}>
                                        {knobs.chorus.name}
                                    </Typography>
                                </Tab>
                                <Tab variant="plain" color="primary" indicatorInset>
                                    <Typography level="body-sm" startDecorator={spatialAudioStatus ? <ActiveEffect sx={{ color: Basic.colors.green }} fontSize='inherit' /> : <InactiveEffect fontSize='inherit' />}>
                                        {knobs.spatial_audio.name}
                                    </Typography>
                                </Tab>
                                <Tab variant="plain" color="primary" indicatorInset>
                                    <Typography level="body-sm" startDecorator={compressorStatus ? <ActiveEffect sx={{ color: Basic.colors.green }} fontSize='inherit' /> : <InactiveEffect fontSize='inherit' />}>
                                        {knobs.compressor.name}
                                    </Typography>
                                </Tab>
                            </TabList>
                            <TabPanel sx={{ paddingLeft: '5px', paddingRight: '5px' }} value={0}>
                                <EqualizerKnobs eqStatus={equalizerStatus} setEqStatus={setEqualizerStatus} sendStatus={sendStatus} resetStatus={resetStatus} sliderState={equalizerKnobs} setSliderState={setEqualizerKnobs} eq_knobs={knobs.equalizer} />
                            </TabPanel>
                            <TabPanel value={1}>
                                <Tabs
                                    sx={{ padding: '0' }}
                                    size="sm"
                                    aria-label="Reverb"
                                    orientation="vertical"
                                >
                                    <TabList sx={{
                                        pt: 0,
                                        justifyContent: 'top',
                                        [`&& .${tabClasses.root}`]: {
                                            flex: 'initial',
                                            bgcolor: 'transparent',
                                            '&:hover': {
                                                bgcolor: 'transparent',
                                            },
                                            '&:focus': {
                                                bgcolor: 'transparent',
                                            },
                                            [`&.${tabClasses.selected}`]: {
                                                color: 'primary.plainColor',
                                                '&::after': {
                                                    width: 2,
                                                    borderTopLeftRadius: 3,
                                                    borderTopRightRadius: 3,
                                                    bgcolor: 'primary.500',
                                                },
                                            },
                                        },
                                    }} >
                                        <Tab variant="plain" color="primary" indicatorInset>
                                            <Typography level="body-sm" startDecorator={reverbStatus ? <ActiveEffect sx={{ color: Basic.colors.green }} fontSize='inherit' /> : <InactiveEffect fontSize='inherit' />}>
                                                {Basic.l('pro')}
                                            </Typography>
                                        </Tab>
                                        <Tab variant="plain" color="primary" indicatorInset>
                                            <Typography level="body-sm" startDecorator={basicreverbStatus ? <ActiveEffect sx={{ color: Basic.colors.green }} fontSize='inherit' /> : <InactiveEffect fontSize='inherit' />}>
                                                {Basic.l('basic')}
                                            </Typography>
                                        </Tab>
                                    </TabList>
                                    <TabPanel sx={{ padding: '0' }} value={0}>
                                        <Grid container spacing={0} sx={{ flexGrow: 1 }}>
                                            <Grid xs>
                                                <EqualizerKnobs eqStatus={reverbStatus} setEqStatus={setReverbStatus} sendStatus={sendStatus} resetStatus={resetStatus} sliderState={reverbKnobs} setSliderState={setReverbKnobs} eq_knobs={knobs.reverb} />
                                            </Grid>
                                            <Grid xs="auto" sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }} >
                                                <EffectSidebar effectKnobs={reverbKnobs} setEffectStatus={setReverbStatus} effectStatus={reverbStatus} effect="reverb" reset={setReverbKnobs} name="pro_reverb" />
                                            </Grid>
                                        </Grid>
                                    </TabPanel>
                                    <TabPanel sx={{ padding: '0' }} value={1}>
                                        <Grid container spacing={0} sx={{ flexGrow: 1 }}>
                                            <Grid xs>
                                                <EqualizerKnobs eqStatus={basicreverbStatus} setEqStatus={setBasicreverbStatus} sendStatus={sendStatus} resetStatus={resetStatus} sliderState={basicreverbKnobs} setSliderState={setBasicreverbKnobs} eq_knobs={knobs.basic_reverb} />
                                            </Grid>
                                            <Grid xs="auto" sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }} >
                                                <EffectSidebar effectKnobs={basicreverbKnobs} setEffectStatus={setBasicreverbStatus} effectStatus={basicreverbStatus} effect="basic_reverb" reset={setBasicreverbKnobs} name="basic_reverb" />
                                            </Grid>
                                        </Grid>
                                    </TabPanel>
                                </Tabs>
                            </TabPanel>
                            <TabPanel value={2}>
                                <Grid container spacing={0} sx={{ flexGrow: 1 }}>
                                    <Grid xs>
                                        <EqualizerKnobs eqStatus={chorusStatus} setEqStatus={setChorusStatus} sendStatus={sendStatus} resetStatus={resetStatus} sliderState={chorusKnobs} setSliderState={setChorusKnobs} eq_knobs={knobs.chorus} />
                                    </Grid>
                                    <Grid xs="auto" sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }} >
                                        <EffectSidebar effectKnobs={chorusKnobs} setEffectStatus={setChorusStatus} effectStatus={chorusStatus} effect="chorus" reset={setChorusKnobs} name="chorus" />
                                    </Grid>
                                </Grid>
                            </TabPanel>
                            <TabPanel value={3}>
                                <Grid container spacing={0} sx={{ flexGrow: 1 }}>
                                    <Grid xs>
                                        <SpatialAudioArea
                                            eq_knobs={knobs.spatial_audio}
                                            sendStatus={sendStatus}
                                            resetStatus={resetStatus}
                                            spatialAudio={spatialAudio}
                                            setSpatialAudio={setSpatialAudio}
                                            spatialAudioStatus={spatialAudioStatus}
                                            setSpatialAudioStatus={setSpatialAudioStatus}
                                        />
                                    </Grid>
                                    <Grid xs="auto" sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }} >
                                        <EffectSidebar effectKnobs={spatialAudio} setEffectStatus={setSpatialAudioStatus} effectStatus={spatialAudioStatus} effect="spatial_audio" reset={setSpatialAudio} name="spatial_audio" />
                                    </Grid>
                                </Grid>
                            </TabPanel>
                            <TabPanel value={4}>
                                <Grid container spacing={0} sx={{ flexGrow: 1 }}>
                                    <Grid xs>
                                        <EqualizerKnobs eqStatus={compressorStatus} setEqStatus={setCompresserStatus} sendStatus={sendStatus} resetStatus={resetStatus} sliderState={compressorKnobs} setSliderState={setCompresserKnobs} eq_knobs={knobs.compressor} />
                                    </Grid>
                                    <Grid xs="auto" sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }} >
                                        <EffectSidebar effectKnobs={compressorKnobs} setEffectStatus={setCompresserStatus} effectStatus={compressorStatus} effect="compressor" reset={setCompresserKnobs} name="compressor" />
                                    </Grid>
                                </Grid>
                            </TabPanel>
                        </Tabs>
                    </Sheet>

                    <Suspense>
                        <ModalComponents.ProfilesModal profilesList={profilesList} setProfilesList={setProfilesList} open={profilesModalStatus} setOpen={setProfilesModalStatus} anchor="left" size="lg" loadProfile={updateLoadedSettings} currentProfile={profile} profileManager={profileManager} sendMessage={sendMessage} currentTab={currentTab} tabSettings={tabSettings} requestProfile={requestProfile} />                        
                        <ModalComponents.SettingsModal open={settingsModal} setOpen={openSettingsModal} setProfilesList={setProfilesList} setProfilesModalStatus={setProfilesModalStatus} anchor="right" size="lg" profileManager={profileManager}  />
                        <ModalComponents.BaseVolumeModal sendStatus={sendStatus} baseVolume={baseVolume} setBaseVolume={setBaseVolume} eq_knobs={knobs.base_volume} open={baseVolumeModal} setOpen={openBaseVolumeModal} sendMessage={sendMessage} tabSettings={tabSettings} currentTab={currentTab} />
                        <ModalComponents.NoiseModal resetStatus={resetStatus} sendStatus={sendStatus} noiseStatus={noiseStatus} setNoiseStatus={setNoiseStatus} noise={noise} setNoise={setNoise} eq_knobs={knobs.noise} open={noiseModal} setOpen={openNoiseModal} sendMessage={sendMessage} currentTab={currentTab} knobs={knobs} />
                        <ModalComponents.AudioModeModal resetStatus={resetStatus} sendStatus={sendStatus} audioMode={audioMode} setAudioMode={setAudioMode} eq_knobs={knobs.audio_mode} open={audioModeModal} setOpen={openAudioModeModal} sendMessage={sendMessage} currentTab={currentTab} tabSettings={tabSettings} />
                        <ModalComponents.PresetsModal open={equalizerPresetsModal} setOpen={openEqualizerPresetsModal} anchor="top" size="sm" presets={Presets.equalizer} setResetStatus={setResetStatus} setPreset={setEqualizerKnobs} currentPreset={equalizerKnobs} />
                        <ModalComponents.PresetsModal open={effectPresetsModal} setOpen={openEffectPresetsModal} anchor="top" size="sm" presets={effectPresets.presets} setResetStatus={setResetStatus} setPreset={effectPresets.callback} currentPreset={effectPresets.current} />
                    </Suspense>
                </Sheet>
            </div>
        </CssVarsProvider>
    );
}