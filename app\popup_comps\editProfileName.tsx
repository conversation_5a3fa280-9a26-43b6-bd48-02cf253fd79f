import Basic from '../Basic';
import React, { useState } from 'react';
import Box from '@mui/joy/Box';
import Button from '@mui/joy/Button';
import { Transition } from 'react-transition-group';
import Modal from '@mui/joy/Modal';
import ModalDialog from '@mui/joy/ModalDialog';
import DialogTitle from '@mui/joy/DialogTitle';
import DialogContent from '@mui/joy/DialogContent';
import Input from '@mui/joy/Input';
import Alert from '@mui/joy/Alert';
import FormHelperText from '@mui/joy/FormHelperText';

// Icons
import InfoOutlined from '@mui/icons-material/InfoOutlined';
import SaveIcon from '@mui/icons-material/Save';

export default function EditProfileName({ defaultProfileName, note = '', callback = (name: any) => { } }) {

    const [open, setOpen] = useState(true);
    const [error, setError] = useState(false);
    const [profileName, setProfileName] = useState(defaultProfileName);

    function saveAction() {
        setError(false);
        if (profileName != '' && profileName.length > 0) {
            callback(profileName);
            setOpen(false);
        } else {
            setError(true);
        }
    }

    return (
        <React.Fragment>
            <Transition in={open} timeout={100}>
                {(state: string) => (
                    <Modal
                        keepMounted
                        open={!['exited', 'exiting'].includes(state)}
                        onClose={() => setOpen(false)}
                        slotProps={{
                            backdrop: {
                                sx: {
                                    opacity: 0,
                                    backdropFilter: 'none',
                                    transition: `opacity 200ms, backdrop-filter 200ms`,
                                    ...{
                                        entering: { opacity: 1, backdropFilter: 'blur(8px)' },
                                        entered: { opacity: 1, backdropFilter: 'blur(8px)' },
                                    }[state],
                                },
                            },
                        }}
                        sx={{
                            visibility: state === 'exited' ? 'hidden' : 'visible',
                        }}
                    >
                        <ModalDialog
                            sx={{
                                opacity: 0,
                                transition: `opacity 300ms`,
                                ...{
                                    entering: { opacity: 1 },
                                    entered: { opacity: 1 },
                                }[state],
                            }}
                        >
                            <DialogTitle>{Basic.l('profile_name')}</DialogTitle>
                            <DialogContent>
                                <Input
                                    variant="soft"
                                    placeholder={Basic.l('profile_name') + "..."}
                                    value={profileName}
                                    onChange={(event) => {
                                        setProfileName(event.target.value);
                                        if (event.target.value != '' && event.target.value.length > 0) {
                                            setError(false);
                                        } else {
                                            setError(true);
                                        }
                                    }}
                                    onKeyDown={(event) => {
                                        if (event.key === 'Enter') {
                                            event.preventDefault(); // Prevent the default Enter key behavior (e.g., submitting a form)
                                            saveAction();
                                        }
                                    }}
                                />
                                {(error) && (<FormHelperText sx={{ color: 'danger.400' }}>
                                    <InfoOutlined sx={{ fontSize: '15px', px: 0.5 }} /> {Basic.l('write_name')}
                                </FormHelperText>)}
                                <Button
                                    variant="soft"
                                    color="neutral"
                                    sx={{
                                        mt: 1,
                                        '&:hover svg': {
                                            color: Basic.colors.green,
                                        },
                                    }}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        saveAction();
                                    }}
                                >
                                    <SaveIcon sx={{ px: 0.5 }} fontSize="small" /> {Basic.l('save')}
                                </Button>
                                {(note != '' && note != undefined) && (
                                    <Box sx={{ mt: 1 }}>
                                        <Alert size="sm">{note}</Alert>
                                    </Box>
                                )}
                            </DialogContent>
                        </ModalDialog>
                    </Modal>
                )}
            </Transition>
        </React.Fragment>
    );
}