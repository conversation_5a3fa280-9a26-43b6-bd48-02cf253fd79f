import Equalizer from '../app/Equalizer';

if (typeof chrome === 'undefined') {
    let chrome: any = browser;
}

chrome.runtime.onMessage.addListener(async (message) => {
    if (message.target === 'offscreen') {
        switch (message.type) {
            case 'start-equalizer':
                startEqualizer(message.data, message.tabid, message.profile);
                break;
            case 'stop-equalizer':
                stopEqualizer(message.tabid);
                break;
            case 'destroy-equalizer':
                destroyEqualizer(message.tabid);
                break;
            case 'change-equalizer':
                Equalizer.changeEqualizer(message.tabid, message.data);
                break;
            case 'load-profile':
                Equalizer.loadProfile(message.tabid, message.data);
                break;
            case 'change-volume':
                Equalizer.changeVolume(message.tabid, message.data);
                break;
            case 'change-base-volume':
                Equalizer.changeBaseVolume(message.tabid, message.data);
                break;
            case 'change-spatial-audio':
                Equalizer.changeSpatialAudio(message.tabid, {
                    shape: message.data[0],
                    duration: message.data[1]
                });
                break;
            case 'change-noise':
                Equalizer.changeNoise(message.tabid, {
                    type: message.data[0],
                    volume: message.data[1]
                });
                break;
            case 'change-audio-mode':
                Equalizer.changeAudioMode(message.tabid, message.data);
                break;
            case 'change-reverb':
                Equalizer.changeReverb(message.tabid, {
                    lowCut: message.data[0],
                    highCut: message.data[1],
                    wetLevel: message.data[2],
                    dryLevel: message.data[3],
                    level: message.data[4],
                });
                break;
            case 'change-basic-reverb':
                Equalizer.changeBasicReverb(message.tabid, {
                    decay: message.data[0],
                    preDelay: message.data[1],
                    wet: message.data[2]
                });
                break;
            case 'change-compressor':
                Equalizer.changeCompressor(message.tabid, {
                    threshold: message.data[0],
                    attack: message.data[1],
                    release: message.data[2],
                    makeupGain: message.data[3],
                    ratio: message.data[4],
                    knee: message.data[5],
                });
                break;
            case 'change-chorus':
                Equalizer.changeChorus(message.tabid, {
                    frequency: message.data[0],
                    delayTime: message.data[1],
                    depth: message.data[2],
                    spread: message.data[3],
                    wet: message.data[4],
                });
                break;
            case 'stop-effect':
                switch (message.data) {
                    case 'reverb':
                        Equalizer.stopReverb(message.tabid);
                        break;
                    case 'basic_reverb':
                        Equalizer.stopBasicReverb(message.tabid);
                        break;
                    case 'chorus':
                        Equalizer.stopChorus(message.tabid);
                        break;
                    case 'compressor':
                        Equalizer.stopCompressor(message.tabid);
                        break;
                    case 'spatial_audio':
                        Equalizer.stopSpatialAudio(message.tabid);
                        break;
                    case 'noise':
                        Equalizer.stopNoise(message.tabid);
                        break;
                }
                break;
            default:
                throw new Error('Unrecognized message: ' + message.type);
        }
    }
});

async function startEqualizer(streamId: string, tabId: string, profile: any = {}) {
    Equalizer.init(tabId, streamId, profile);

    window.location.hash = window.location.hash + '/' + tabId;
}

function cleanup_ports(tabId) {
    if(messenger_ports[tabId]){
        messenger_ports[tabId].disconnect();
    }
    if(profile_ports[tabId]){
        profile_ports[tabId].disconnect();
    }

    delete messenger_ports[tabId];
    delete profile_ports[tabId];
}

async function stopEqualizer(tabId: string) {
    Equalizer.stop(tabId);

    window.location.hash = window.location.hash.replace('/' + tabId, '');

    cleanup_ports(tabId);
}


async function destroyEqualizer(tabId: string) {
    Equalizer.stop(tabId);
    Equalizer.destroy(tabId);

    window.location.hash = window.location.hash.replace('/' + tabId, '');

    cleanup_ports(tabId);
}

const visualiser_intv: Record<string, any> = [];

async function showVisualiser(tabContext: any, callback: any = function () { }) {

    if (tabContext && tabContext.audioContext) {
        // Create an AnalyserNode to process the audio
        const analyser = tabContext.audioContext.createAnalyser();
        tabContext.audioSource.connect(analyser);

        // Set up the analyser
        analyser.fftSize = 256;
        analyser.smoothingTimeConstant = 0.6;
        analyser.maxDecibels = 0;
        analyser.minDecibels = -100;

        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);

        // Process the audio and send the data to the popup
        function processAudio() {
            //analyser.getByteTimeDomainData(dataArray);
            analyser.getByteFrequencyData(dataArray);
            if (tabContext?.settings?.connected) {
                callback({ type: 'audioData', frequencyData: Array.from(dataArray), frequencyBinCount: bufferLength });
            }
            if (!messenger_ports[tabContext.id]) {
                cancelVisualiser(tabContext);
            }
        }

        // this is 62 frames/s
        visualiser_intv[tabContext.id] = setInterval(processAudio, 16);
    }
}

async function cancelVisualiser(currentTabId: any, callback: any = function () { }) {
    if (visualiser_intv[currentTabId]) {
        clearInterval(visualiser_intv[currentTabId]);
        delete visualiser_intv[currentTabId];
    }
}

const messenger_ports: Record<string, chrome.runtime.Port> = {};
const profile_ports: Record<string, chrome.runtime.Port> = {};

// Listen for messages from the popup
chrome.runtime.onConnect.addListener((port) => {
    if (port.name === "popupConnection" || port.name === "requestingProfile") {
        const onMessageListener = (message: any) => {
            if (message.type === 'tabId') {
                const tabId = message.tabId;

                if (port.name === "popupConnection") {
                    messenger_ports[tabId] = port;

                    const cleanupPort = () => {
                        cancelVisualiser(tabId);
                        delete messenger_ports[tabId];
                        delete profile_ports[tabId];
                    };

                    port.onDisconnect.addListener(cleanupPort);

                    Equalizer.fetchTabSettings(tabId, async function (tabSettings: any) {
                        if (messenger_ports[tabId]) {
                            messenger_ports[tabId].postMessage({
                                type: 'loadTabSettings',
                                settings: tabSettings ?? null
                            });
                        }
                    });

                    Equalizer.connectVisualizer(tabId, async function (tabContext: any) {
                        showVisualiser(tabContext, function (visualizer_content: any) {
                            if (messenger_ports[tabContext.id]) {
                                messenger_ports[tabContext.id].postMessage(visualizer_content);
                            } else {
                                cancelVisualiser(tabContext.id);
                            }
                        });
                    });
                }

                if (port.name === "requestingProfile") {
                    profile_ports[tabId] = port;

                    const cleanupProfilePort = () => {
                        delete profile_ports[tabId];
                    };

                    port.onDisconnect.addListener(cleanupProfilePort);

                    Equalizer.fetchTabSettings(tabId, async function (tabSettings: any) {
                        if (profile_ports[tabId]) {
                            profile_ports[tabId].postMessage({
                                type: 'respondingProfile',
                                settings: tabSettings ?? null
                            });
                        }
                    });
                }
            }
            return true;
        };

        port.onMessage.addListener(onMessageListener);

        port.onDisconnect.addListener(() => {
            port.onMessage.removeListener(onMessageListener);

            // Cleanup messenger_ports if it's a popupConnection
            if (port.name === "popupConnection") {
                const tabId = Object.keys(messenger_ports).find(key => messenger_ports[key] === port);
                if (tabId) {
                    cancelVisualiser(tabId);
                    delete messenger_ports[tabId];
                }
            }

            // Cleanup profile_ports if it's a requestingProfile
            if (port.name === "requestingProfile") {
                const tabId = Object.keys(profile_ports).find(key => profile_ports[key] === port);
                if (tabId) {
                    delete profile_ports[tabId];
                }
            }
        });
    }
});