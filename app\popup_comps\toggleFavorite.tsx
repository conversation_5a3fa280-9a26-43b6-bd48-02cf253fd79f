const toggleFavorite = async (key: string, profilesList: any, setProfilesList: any, profileManager: any) => {
    if (key != 'default') {
        let newList = { ...profilesList };
        if (!profilesList[key].favorite) {
            newList[key].favorite = true;
            await profileManager.markAsFavorite(key);
        } else {
            newList[key].favorite = false;
            await profileManager.removeFavorite(key);
        }
        setProfilesList(newList);
    }
};

export default toggleFavorite;