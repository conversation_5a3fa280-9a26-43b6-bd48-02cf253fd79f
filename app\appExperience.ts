import { Storage } from "@plasmohq/storage";

if (!process.env.PLASMO_PUBLIC_GTAG_ID) {
  throw new Error("PLASMO_PUBLIC_GTAG_ID environment variable not set.");
}

if (!process.env.PLASMO_PUBLIC_GTAG_API_KEY) {
  throw new Error("PLASMO_PUBLIC_GTAG_API_KEY environment variable not set.");
}

const GA_ENDPOINT = "https://www.google-analytics.com/mp/collect";
const gtagId = process.env.PLASMO_PUBLIC_GTAG_ID;
const secretApiKey = process.env.PLASMO_PUBLIC_GTAG_API_KEY;

type CollectEventPayload = {
  name: string
  params?: any
}

const AnalyticsEvent = async (events: CollectEventPayload[]) => {
  const storage = new Storage({
    area: "sync"
  });

  let clientId = await storage.get("client_id");

  // Just incase the client ID was not set on install.
  if (!clientId) {
    clientId = self.crypto.randomUUID();
    await storage.set("client_id", clientId);
  }

  const fetched = await fetch(
    `${GA_ENDPOINT}?measurement_id=${gtagId}&api_secret=${secretApiKey}`,
    {
      method: "POST",
      body: JSON.stringify({
        client_id: clientId,
        events
      })
    }
  );

  return fetched;
}

const appExperience = function (event_path: string, event_name: string, event_object: any): Promise<void> {
    return new Promise((resolve) => {
        try {
            AnalyticsEvent([
                {
                  name: event_name,
                  params: event_object
                }
            ]);
            resolve();
        } catch (e) {
            resolve();
        }
    });
}

export default appExperience;