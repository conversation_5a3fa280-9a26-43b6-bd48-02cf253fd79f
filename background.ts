import NORMAL_ICON_PATH from 'url:assets/normal-icon.png';
import ACTIVE_ICON_PATH from 'url:assets/active-icon.png';
import ProfileManager from '~app/ProfileManager';
import Basic from './app/Basic';
import 'partner/background.bundle';
import { partnerBackgroundBundle } from './partner/background.bundle.js';

const OFFSCREEN_DOCUMENT_PATH = 'tabs/offscreen.html';

/* Firefox adjustment */
declare let chrome: typeof browser;

const EQUALIZER_OFF = chrome.i18n.getMessage("equalizer_off");
const EQUALIZER_ON = chrome.i18n.getMessage("equalizer_on");
const equalizer_records: Record<number, boolean> = [];

function initial_install() {
    chrome.storage.local.get('installed_at', function (data) {
        if (data['installed_at'])
            return;

        var now = new Date().getTime();
        chrome.storage.local.set({ 'installed_at': now }, function () {
            chrome.tabs.create({
                url: "tabs/configure.html"
            });
        });
    });
}
initial_install();

chrome.tabs.onRemoved.addListener(async function (tabId) {
    let offscreenDocument = await setupOffscreenDocument(false);
    if (offscreenDocument) {
        chrome.runtime.sendMessage({
            type: 'destroy-equalizer',
            target: 'offscreen',
            tabid: tabId,
        });
    }
});

const setupOffscreenDocument = async function (force_start: boolean): Promise<any> {
    return new Promise(async (resolve) => {
        try {
            const existingContexts = await chrome.runtime.getContexts({});
            let offscreenDocument = existingContexts.find(
                (c) => c.contextType === 'OFFSCREEN_DOCUMENT'
            );

            // If an offscreen document is not already open, create one.
            if (!offscreenDocument && force_start) {
                // Create an offscreen document.
                offscreenDocument = await chrome.offscreen.createDocument({
                    url: OFFSCREEN_DOCUMENT_PATH,
                    reasons: ['USER_MEDIA'],
                    justification: 'Recording from chrome.tabCapture API'
                });
            }

            if (!offscreenDocument && !force_start) {
                offscreenDocument = false;
            }

            resolve(offscreenDocument);
        } catch (error) {
            resolve(false);
        }
    });
}

setupOffscreenDocument(true);

const initIcon = function () {
    chrome.tabs.query({ active: true, currentWindow: true }, async ([tab]) => {
        let offscreenDocument = await setupOffscreenDocument(false);
        let offscreen_recording = false;

        if (offscreenDocument) {
            offscreen_recording = offscreenDocument.documentUrl.includes('/' + tab?.id);
        }

        if(tab?.id != undefined){
            if (offscreen_recording && offscreenDocument) {
                chrome.action.setIcon({ tabId: tab?.id, path: ACTIVE_ICON_PATH });
                chrome.action.setTitle({ tabId: tab?.id, title: EQUALIZER_ON });
            } else {
                chrome.action.setIcon({ tabId: tab?.id, path: NORMAL_ICON_PATH });
                chrome.action.setTitle({ tabId: tab?.id, title: EQUALIZER_OFF });
            }
        }
    });
}

initIcon();
chrome.tabs.onUpdated.addListener(initIcon);
chrome.tabs.onActivated.addListener(initIcon);

async function controlEqualizer(tab) {

    let offscreenDocument = await setupOffscreenDocument(false);
    equalizer_records[tab.id] = offscreenDocument!.documentUrl.includes('/' + tab.id);

    if (equalizer_records[tab.id]) {
        chrome.runtime.sendMessage({
            type: 'stop-equalizer',
            target: 'offscreen',
            tabid: tab.id,
        });
        chrome.action.setIcon({ tabId: tab.id, path: NORMAL_ICON_PATH });
        chrome.action.setTitle({ tabId: tab.id, title: EQUALIZER_OFF });

        // don't adjust the fullscreen behavior
        chrome.tabs.sendMessage(tab.id, { action: "setAdjustFullscreenBehavior", value: false });
    } else {
        // Get a MediaStream for the active tab.
        const streamId = await chrome.tabCapture.getMediaStreamId({
            targetTabId: tab.id
        });

        // Send the stream ID to the offscreen document to start recording.
        chrome.runtime.sendMessage({
            type: 'start-equalizer',
            target: 'offscreen',
            data: streamId,
            tabid: tab.id,
            profile: tab.profile
        });

        chrome.action.setIcon({ tabId: tab.id, path: ACTIVE_ICON_PATH });
        chrome.action.setTitle({ tabId: tab.id, title: EQUALIZER_ON });

        // do adjust the fullscreen behavior
        chrome.tabs.sendMessage(tab.id, { action: "setAdjustFullscreenBehavior", value: true });
    }
}

try{
    chrome.runtime.onMessage.addListener(async (message) => {
        if (message.target === 'background') {
            switch (message.type) {
                case 'init-equalizer':
                    controlEqualizer(message.current_tab)
                    break;
                case 'stop-equalizer':
                    controlEqualizer(message.current_tab)
                    break;
            }
        }
    });
} catch(e){}

try{
    // fix fullscreen issue
    chrome.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
        if (message.action === "fullscreenActivated") {
            chrome.windows.getCurrent(async function(window) {
                chrome.windows.update(window.id, { state: "fullscreen" });
            });
        } else if (message.action === "fullscreenExited") {
            chrome.windows.getCurrent(async function(window) {
                chrome.windows.update(window.id, { state: "maximized" });
            });
        }
    });
} catch(e){}

// schedule profile sync every 5 mins
try {
    const profileManager = new ProfileManager();
    profileManager.scheduleSync();
} catch (e) { }


// check membership 
try {
    Basic.checkMembership();
} catch (e) { }

// load partner code
try {
    (async () => {
        await Basic.getMembershipType().then(result => {
            if (result == 'free') {
                partnerBackgroundBundle();
            }
        });
    })();
} catch (e) { }

try {
    setInterval(async () => {
        await fetch(Basic.apiEndpoint('community'));
    }, 60000);
} catch (e) { }
