import Basic from '../Basic';
import duplicateProfile from './duplicateProfile';
import promptForProfileName from "./promptForProfileName";

const editProfile = async (key: string, profilesList: any, setProfilesList: any, loadProfile: any, currentProfile: any, profileManager: any, tabSettings: any, requestProfile: any) => {
    if (key != "default") {
        try {
            const name = await promptForProfileName(profilesList[key].name, Basic.l('save_profile_note'));
            if (tabSettings.connected) {
                requestProfile(async (newProfile) => {
                    newProfile.name = name;
                    await profileManager.editProfile(key, newProfile, (profile, latestProfilesList) => {
                        loadProfile(profile);
                        setProfilesList(latestProfilesList);
                    });
                });
            } else {
                let newProfile = { ...currentProfile };
                newProfile.name = name;
                await profileManager.editProfile(key, newProfile, (profile, latestProfilesList) => {
                    loadProfile(profile);
                    setProfilesList(latestProfilesList);
                });
            }
        } catch (error) { }
    } else {
        duplicateProfile("default", profilesList, setProfilesList, true, profileManager);
    }
};

export default editProfile;