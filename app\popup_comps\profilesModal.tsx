// import Basic from '../Basic';
// import React, { useState, useEffect } from 'react';
// import Item from '../Item';
// import Box from '@mui/joy/Box';
// import Button from '@mui/joy/Button';
// import Grid from '@mui/joy/Grid';
// import Tooltip from '@mui/joy/Tooltip';
// import IconButton from '@mui/joy/IconButton';
// import Typography from '@mui/joy/Typography';
// import DialogTitle from '@mui/joy/DialogTitle';
// import DialogContent from '@mui/joy/DialogContent';
// import { Scrollbar } from 'react-scrollbars-custom';
// import Drawer from '@mui/joy/Drawer';
// import List from '@mui/joy/List';
// import ListItem from '@mui/joy/ListItem';
// import ListItemButton from '@mui/joy/ListItemButton';

// // Icons
// import InfoOutlined from '@mui/icons-material/InfoOutlined';
// import SaveIcon from '@mui/icons-material/Save';
// import SaveAsIcon from '@mui/icons-material/Add';
// import FavoriteIcon from '@mui/icons-material/Favorite';
// import DeleteIcon from '@mui/icons-material/Delete';
// import ContentCopyIcon from '@mui/icons-material/ContentCopy';
// import PowerIcon from '@mui/icons-material/Power';
// import SyncIcon from '@mui/icons-material/CloudSync';
// import switchProfile from './switchProfile';
// import addNewProfile from './addNewProfile';
// import editProfile from './editProfile';
// import toggleFavorite from './toggleFavorite';
// import duplicateProfile from './duplicateProfile';
// import deleteProfile from './deleteProfile';

// const ProfilesModal = function ({ open, setOpen, anchor, size, loadProfile, currentProfile, profilesList, setProfilesList, profileManager, sendMessage, currentTab, tabSettings, requestProfile}) {

//     const [syncLoading, setSyncLoading] = useState(false);

//     const fetchProfiles = async () => {
//         try {
//             const profiles: any = await profileManager.listProfiles();
//             setProfilesList(profiles);
//         } catch (error) { }
//     };

//     const syncProfiles = async () => {
//         setSyncLoading(true);
//         setTimeout(() => {
//             profileManager.syncGlobal(() => {
//                 fetchProfiles();
//                 setSyncLoading(false);
//             });
//         }, 1000);
//     };

//     useEffect(() => {
//         fetchProfiles();
//     }, []);

//     return (
//         <React.Fragment>
//             <Drawer
//                 anchor={['top', 'bottom', 'left', 'right'].includes(anchor) ? anchor : 'bottom'}
//                 size={['sm', 'md', 'lg'].includes(size) ? size : 'md'}
//                 open={open}
//                 onClose={() => setOpen(false)} >

//                 <Grid container sx={{ flexGrow: 1 }}>
//                     <Grid xs={3}>
//                         <Item><DialogTitle sx={{ px: 0.5, fontSize: '15px', lineHeight: '35px' }}>{Basic.l('profiles')}</DialogTitle></Item>
//                     </Grid>
//                     <Grid xs>
//                         <Item>
//                             <Box sx={{ display: 'flex', gap: 0.5, alignItems: Basic.l('extensionDirection') == 'rtl' ? 'left' : 'right', justifyContent: Basic.l('extensionDirection') == 'rtl' ? 'left' : 'right' }} >
//                                 <Button
//                                     variant="plain"
//                                     color="neutral"
//                                     sx={{
//                                         '&:hover svg': {
//                                             color: Basic.colors.green,
//                                         },
//                                     }}
//                                     onClick={(e) => {
//                                         e.stopPropagation();
//                                         addNewProfile(currentProfile, setProfilesList, true, profileManager, tabSettings, requestProfile);
//                                     }}
//                                 >
//                                     <SaveAsIcon fontSize="small" sx={{ pr: Basic.l('extensionDirection') == 'rtl' ? 0 : 1, pl: Basic.l('extensionDirection') == 'rtl' ? 1 : 0 }} /> {Basic.l('add_profile')}
//                                 </Button>
//                                 <Button
//                                     loading={syncLoading}
//                                     variant="plain"
//                                     color="neutral"
//                                     sx={{
//                                         '&:hover svg': {
//                                             color: Basic.colors.green,
//                                         },
//                                     }}
//                                     onClick={(e) => {
//                                         e.stopPropagation();
//                                         syncProfiles();
//                                     }}
//                                 >
//                                     <SyncIcon fontSize="small" sx={{ pr: Basic.l('extensionDirection') == 'rtl' ? 0 : 1, pl: Basic.l('extensionDirection') == 'rtl' ? 1 : 0 }} /> {Basic.l('sync')}
//                                 </Button>
//                             </Box>
//                         </Item>
//                     </Grid>
//                 </Grid>

//                 <DialogContent>
//                     <Scrollbar>
//                         <Box sx={{ px: 1 }}>
//                             <List>
//                                 {Object.entries(profilesList).map(([index, profile]: [any, any]) => (
//                                     <ListItem
//                                         onClick={() => {
//                                             switchProfile(index, setProfilesList, loadProfile, profileManager, sendMessage, currentTab);
//                                         }}
//                                         key={index}
//                                     >
//                                         <ListItemButton sx={{
//                                             borderRadius: '7px',
//                                             py: 0.7,
//                                             mb: 0.5,
//                                             backgroundColor: profile.status ? 'background.level1' : 'transparent',
//                                             '&:hover .plugIcon': {
//                                                 color: Basic.colors.green,
//                                             },
//                                             border: 0,
//                                             borderLeft: Basic.l('extensionDirection') == 'rtl' ? 'none' : '2px solid',
//                                             borderRight: Basic.l('extensionDirection') == 'rtl' ? '2px solid' : 'none',
//                                             borderColor: profile.status ? Basic.colors.green : 'transparent'
//                                         }}>
//                                             <Typography startDecorator={<PowerIcon className="plugIcon" sx={{ color: profile.status ? Basic.colors.green : '' }} fontSize="small" />} noWrap sx={{ width: '100%', fontSize: 'md', fontWeight: 'md' }}>
//                                                 {profile.name}
//                                             </Typography>

//                                             <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center', justifyContent: 'center' }}>
//                                                 {(index !== 'default' && profile.status) && (
//                                                     <IconButton
//                                                         disabled={index === 'default'}
//                                                         variant="plain"
//                                                         sx={{
//                                                             '&:hover': {
//                                                                 color: Basic.colors.green,
//                                                             },
//                                                         }}
//                                                         onClick={(e) => {
//                                                             e.stopPropagation();
//                                                             editProfile(index, profilesList, setProfilesList, loadProfile, currentProfile, profileManager, tabSettings, requestProfile);
//                                                         }}
//                                                     >
//                                                         <SaveIcon sx={{ fontSize: '18px' }} />
//                                                     </IconButton>
//                                                 )}
//                                                 {(index !== 'default') && (
//                                                     <IconButton
//                                                         disabled={index === 'default'}
//                                                         variant="plain"
//                                                         sx={{
//                                                             '&:hover': {
//                                                                 color: Basic.colors.red,
//                                                             },
//                                                         }}
//                                                         onClick={(e) => {
//                                                             e.stopPropagation();
//                                                             toggleFavorite(index, profilesList, setProfilesList, profileManager);
//                                                         }}
//                                                     >
//                                                         <FavoriteIcon sx={{ color: profile.favorite ? Basic.colors.red : '', fontSize: '18px' }} />
//                                                     </IconButton>
//                                                 )}
//                                                 <IconButton
//                                                     variant="plain"
//                                                     sx={{
//                                                         '&:hover': {
//                                                             color: Basic.colors.blue,
//                                                         },
//                                                     }}
//                                                     onClick={(e) => {
//                                                         e.stopPropagation();
//                                                         duplicateProfile(index, profilesList, setProfilesList, false, profileManager);
//                                                     }} >
//                                                     <ContentCopyIcon sx={{ fontSize: '18px' }} />
//                                                 </IconButton>
//                                                 {(index !== 'default' && profile.status) && (
//                                                     <Tooltip title={Basic.l('active_profile_delete_note')} color="neutral" size="sm" placement={Basic.l('extensionDirection') == 'rtl' ? 'left' : 'right'} variant="soft">
//                                                         <IconButton
//                                                             variant="plain"
//                                                             sx={{
//                                                                 opacity: 0.4,
//                                                                 '&:hover': {
//                                                                     color: Basic.colors.red,
//                                                                 },
//                                                             }}
//                                                             onClick={(e) => {
//                                                                 e.stopPropagation();
//                                                             }} >
//                                                             <DeleteIcon sx={{ fontSize: '18px' }} />
//                                                         </IconButton>
//                                                     </Tooltip>
//                                                 )}
//                                                 {(index !== 'default' && !profile.status) && (
//                                                     <IconButton
//                                                         variant="plain"
//                                                         sx={{
//                                                             '&:hover': {
//                                                                 color: Basic.colors.red,
//                                                             },
//                                                         }}
//                                                         onClick={(e) => {
//                                                             e.stopPropagation();
//                                                             deleteProfile(index, profilesList, setProfilesList, profileManager);
//                                                         }} >
//                                                         <DeleteIcon sx={{ fontSize: '18px' }} />
//                                                     </IconButton>
//                                                 )}
//                                             </Box>

//                                         </ListItemButton>
//                                     </ListItem>
//                                 ))}
//                             </List>

//                         </Box>
//                     </Scrollbar>
//                 </DialogContent>
//                 <Box
//                     sx={{
//                         display: 'flex',
//                         gap: 1,
//                         p: 1,
//                         borderTop: '1px solid',
//                         borderColor: 'divider',
//                     }}
//                 >
//                     <Typography sx={{ fontSize: '12px' }} startDecorator={<InfoOutlined sx={{ fontSize: '15px', px: 0.5 }} />}>
//                         {Basic.l('default_profile_note')}
//                     </Typography>
//                 </Box>
//             </Drawer>
//         </React.Fragment>
//     );
// }

// export default ProfilesModal;




import Basic from '../Basic';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import Item from '../Item';
import Box from '@mui/joy/Box';
import Button from '@mui/joy/Button';
import Grid from '@mui/joy/Grid';
import Tooltip from '@mui/joy/Tooltip';
import IconButton from '@mui/joy/IconButton';
import Typography from '@mui/joy/Typography';
import DialogTitle from '@mui/joy/DialogTitle';
import DialogContent from '@mui/joy/DialogContent';
import { Scrollbar } from 'react-scrollbars-custom';
import Drawer from '@mui/joy/Drawer';
import List from '@mui/joy/List';
import ListItem from '@mui/joy/ListItem';
import ListItemButton from '@mui/joy/ListItemButton';

// Icons
import InfoOutlined from '@mui/icons-material/InfoOutlined';
import SaveIcon from '@mui/icons-material/Save';
import SaveAsIcon from '@mui/icons-material/Add';
import FavoriteIcon from '@mui/icons-material/Favorite';
import DeleteIcon from '@mui/icons-material/Delete';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import PowerIcon from '@mui/icons-material/Power';
import SyncIcon from '@mui/icons-material/CloudSync';
import switchProfile from './switchProfile';
import addNewProfile from './addNewProfile';
import editProfile from './editProfile';
import toggleFavorite from './toggleFavorite';
import duplicateProfile from './duplicateProfile';
import deleteProfile from './deleteProfile';

const ProfilesModal = ({ open, setOpen, anchor, size, loadProfile, currentProfile, profilesList, setProfilesList, profileManager, sendMessage, currentTab, tabSettings, requestProfile }) => {
  const [syncLoading, setSyncLoading] = useState(false);

  const fetchProfiles = useCallback(async () => {
    try {
      const profiles = await profileManager.listProfiles();
      setProfilesList(profiles);
    } catch (error) {
      console.error('Failed to fetch profiles:', error);
    }
  }, [profileManager, setProfilesList]);

  const syncProfiles = useCallback(() => {
    setSyncLoading(true);
    setTimeout(() => {
      profileManager.syncGlobal(() => {
        fetchProfiles();
        setSyncLoading(false);
      });
    }, 1000);
  }, [fetchProfiles, profileManager]);

  useEffect(() => {
    fetchProfiles();
  }, [fetchProfiles]);

  const handleAddProfile = useCallback((e) => {
    e.stopPropagation();
    addNewProfile(currentProfile, setProfilesList, true, profileManager, tabSettings, requestProfile);
  }, [currentProfile, profileManager, requestProfile, setProfilesList, tabSettings]);

  const handleSyncProfiles = useCallback((e) => {
    e.stopPropagation();
    syncProfiles();
  }, [syncProfiles]);

  const handleSwitchProfile = useCallback((index) => {
    switchProfile(index, setProfilesList, loadProfile, profileManager, sendMessage, currentTab);
  }, [currentTab, loadProfile, profileManager, sendMessage, setProfilesList]);

  const profilesListItems = useMemo(() => {
    return Object.entries(profilesList).map(([index, profile]: any) => (
      <ListItem onClick={() => handleSwitchProfile(index)} key={index}>
        <ListItemButton
          sx={{
            borderRadius: '7px',
            py: 0.7,
            mb: 0.5,
            backgroundColor: profile.status ? 'background.level1' : 'transparent',
            border: 0,
            borderLeft: Basic.l('extensionDirection') === 'rtl' ? 'none' : '2px solid',
            borderRight: Basic.l('extensionDirection') === 'rtl' ? '2px solid' : 'none',
            borderColor: profile.status ? Basic.colors.green : 'transparent',
          }}
        >
          <Typography startDecorator={<PowerIcon className="plugIcon" sx={{ color: profile.status ? Basic.colors.green : '' }} fontSize="small" />} noWrap sx={{ width: '100%', fontSize: 'md', fontWeight: 'md' }}>
            {profile.name}
          </Typography>
          <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center', justifyContent: 'center' }}>
            {index !== 'default' && profile.status && (
              <IconButton
                disabled={index === 'default'}
                variant="plain"
                sx={{ '&:hover': { color: Basic.colors.green } }}
                onClick={(e) => {
                  e.stopPropagation();
                  editProfile(index, profilesList, setProfilesList, loadProfile, currentProfile, profileManager, tabSettings, requestProfile);
                }}
              >
                <SaveIcon sx={{ fontSize: '18px' }} />
              </IconButton>
            )}
            {index !== 'default' && (
              <IconButton
                disabled={index === 'default'}
                variant="plain"
                sx={{ '&:hover': { color: Basic.colors.red } }}
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFavorite(index, profilesList, setProfilesList, profileManager);
                }}
              >
                <FavoriteIcon sx={{ color: profile.favorite ? Basic.colors.red : '', fontSize: '18px' }} />
              </IconButton>
            )}
            <IconButton
              variant="plain"
              sx={{ '&:hover': { color: Basic.colors.blue } }}
              onClick={(e) => {
                e.stopPropagation();
                duplicateProfile(index, profilesList, setProfilesList, false, profileManager);
              }}
            >
              <ContentCopyIcon sx={{ fontSize: '18px' }} />
            </IconButton>
            {index !== 'default' && profile.status && (
              <Tooltip title={Basic.l('active_profile_delete_note')} color="neutral" size="sm" placement={Basic.l('extensionDirection') === 'rtl' ? 'left' : 'right'} variant="soft">
                <IconButton
                  variant="plain"
                  sx={{ opacity: 0.4, '&:hover': { color: Basic.colors.red } }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <DeleteIcon sx={{ fontSize: '18px' }} />
                </IconButton>
              </Tooltip>
            )}
            {index !== 'default' && !profile.status && (
              <IconButton
                variant="plain"
                sx={{ '&:hover': { color: Basic.colors.red } }}
                onClick={(e) => {
                  e.stopPropagation();
                  deleteProfile(index, profilesList, setProfilesList, profileManager);
                }}
              >
                <DeleteIcon sx={{ fontSize: '18px' }} />
              </IconButton>
            )}
          </Box>
        </ListItemButton>
      </ListItem>
    ));
  }, [handleSwitchProfile, profilesList]);

  return (
    <React.Fragment>
      <Drawer
        anchor={['top', 'bottom', 'left', 'right'].includes(anchor) ? anchor : 'bottom'}
        size={['sm', 'md', 'lg'].includes(size) ? size : 'md'}
        open={open}
        onClose={() => setOpen(false)}
      >
        <Grid container sx={{ flexGrow: 1 }}>
          <Grid xs={3}>
            <DialogTitle sx={{ px: 0.5, fontSize: '15px', lineHeight: '35px' }}>{Basic.l('profiles')}</DialogTitle>
          </Grid>
          <Grid xs>
            <Box sx={{ display: 'flex', gap: 0.5, alignItems: Basic.l('extensionDirection') === 'rtl' ? 'left' : 'right', justifyContent: Basic.l('extensionDirection') === 'rtl' ? 'left' : 'right' }}>
              <Button
                variant="plain"
                color="neutral"
                sx={{ '&:hover svg': { color: Basic.colors.green } }}
                onClick={handleAddProfile}
              >
                <SaveAsIcon fontSize="small" sx={{ pr: Basic.l('extensionDirection') === 'rtl' ? 0 : 1, pl: Basic.l('extensionDirection') === 'rtl' ? 1 : 0 }} />
                {Basic.l('add_profile')}
              </Button>
              <Button
                loading={syncLoading}
                variant="plain"
                color="neutral"
                sx={{ '&:hover svg': { color: Basic.colors.green } }}
                onClick={handleSyncProfiles}
              >
                <SyncIcon fontSize="small" sx={{ pr: Basic.l('extensionDirection') === 'rtl' ? 0 : 1, pl: Basic.l('extensionDirection') === 'rtl' ? 1 : 0 }} />
                {Basic.l('sync')}
              </Button>
            </Box>
          </Grid>
        </Grid>
        <DialogContent>
          <Scrollbar>
            <Box sx={{ px: 1 }}>
              <List>
                {profilesListItems}
              </List>
            </Box>
          </Scrollbar>
        </DialogContent>
        <Box sx={{ display: 'flex', gap: 1, p: 1, borderTop: '1px solid', borderColor: 'divider' }}>
          <Typography sx={{ fontSize: '12px' }} startDecorator={<InfoOutlined sx={{ fontSize: '15px', px: 0.5 }} />}>
            {Basic.l('default_profile_note')}
          </Typography>
        </Box>
      </Drawer>
    </React.Fragment>
  );
};

export default ProfilesModal;
