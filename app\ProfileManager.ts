import UserProfile from "./UserProfile";

function generateUniqueId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 9);
}

class ProfileManager {
    private profiles: { [id: string]: UserProfile } = {};
    private modifiedProfiles: { local: Set<string>; global: Set<string> } = { local: new Set(), global: new Set() };
    private addedProfiles: { local: Set<string>; global: Set<string> } = { local: new Set(), global: new Set() };
    private deletedProfiles: { local: Set<string>; global: Set<string> } = { local: new Set(), global: new Set() };

    private readonly DEFAULT_PROFILE_ID = 'default';
    private currentProfileId?: string;
    private autoSync: boolean = true;
    private readonly maxProfiles: number = 25;
    private isInitialized: boolean = false; // Flag to track initialization
    private reservedKeys = ["settings", "profile_changes", "installed_at", "membership", "tab_storage", "clientId", "client_id"];

    constructor(autoSync: boolean = true) {
        this.autoSync = autoSync;
        this.initDefaultProfile();
        this.loadProfiles().then(async () => {
            if (!this.isInitialized) {
                this.isInitialized = true;
                await this.initializeProfiles();
            }
        });
    }

    private async initializeProfiles() {
        const activeProfileId = this.getActiveProfileId();
        if (activeProfileId != null) {
            this.switchProfile(activeProfileId);
        } else if (this.currentProfileId == undefined) {
            this.switchProfile(this.DEFAULT_PROFILE_ID);
        }
    }

    private initDefaultProfile() {
        this.profiles[this.DEFAULT_PROFILE_ID] = UserProfile;
    }

    private getActiveProfileId() {
        // await this.waitForInitialization();
        let loopProfiles = {...this.profiles};
        delete loopProfiles[this.DEFAULT_PROFILE_ID];
        for (const id in loopProfiles) {
            if (this.profiles[id].status === true) {
                return id;
            }
        }
        return null;
    }

    private getStorage(storage, key) {
        return new Promise((resolve, reject) => {
            storage.get(key, (result) => {
                if (chrome.runtime.lastError) {
                    reject();
                } else {
                    resolve(result);
                }
            });
        });
    }

    private async loadProfiles(): Promise<void> {
        return new Promise(async (resolve) => {
            try {
                // load local changes
                await this.loadLocalChanges();

                // Load local profiles
                try {
                    let localData = await this.getStorage(chrome.storage.local, null);
                    if(localData){
                        for (const [localKey, localValue] of Object.entries(localData)) {
                            if (!this.reservedKeys.includes(localKey)) {
                                const localId = localKey;
                                const localProfile = JSON.parse(localValue as string);
                                this.profiles[localId] = localProfile;
                            }
                        }
                    }
                } catch (error) {}

                try {
                    let syncedData = await this.getStorage(chrome.storage.sync, null);
                    if(syncedData){
                        for (const [syncedKey, syncedValue] of Object.entries(syncedData)) {
                            if (!this.reservedKeys.includes(syncedKey)) {
                                const syncedId = syncedKey;
                                const syncedProfile = JSON.parse(syncedValue as string);
                                // Merge with local profiles, lossless local data
                                if (!this.profiles[syncedId]) {
                                    this.profiles[syncedId] = syncedProfile;
                                }
                            }
                        }
                    }
                } catch (error) {}

                this.initDefaultProfile();

                this.retainDeletions();


                resolve();

            } catch (error) {
                resolve();
            }
        });
    }

    async waitForInitialization(): Promise<void> {
        return new Promise((resolve) => {
            const checkInitialization = () => {
                if (this.isInitialized) {
                    resolve();
                } else {
                    setTimeout(checkInitialization, 10); // Check again after a short delay
                }
            };
            checkInitialization();
        });
    }

    async addProfile(profile: any, callback = (profile: any, list: any) => { }, switchAfter: boolean = false, importAction = false) {
        await this.waitForInitialization();
        if (Object.keys(this.profiles).length >= this.maxProfiles) {
            console.error('Maximum number of profiles reached.');
            return false;
        }

        const id = generateUniqueId();
        if (this.reservedKeys.includes(id)) {
            console.error('Reserved storage ID: ' + id);
            return false;
        }

        const mergedProfile = this.mergeWithDefaultProfile(profile);
        if(!importAction){
            mergedProfile.favorite = false;
        } else {
            mergedProfile.status = false;
        }
        mergedProfile.name = (mergedProfile.name != undefined && mergedProfile.name != '') ?
            mergedProfile.name : "Profile " + (Object.keys(this.listProfiles()).length + 1);
        this.profiles[id] = mergedProfile;
        this.markProfileAsAdded(id);

        
        if (this.autoSync) {
            this.syncLocal(async () => {
                if(switchAfter){
                    await this.switchProfile(id);
                }
                let newProfilesList = await this.listProfiles();
                callback(newProfilesList[this.currentProfileId], newProfilesList);
            });
        }
        return true;
    }

    private mergeWithDefaultProfile(profile: UserProfile): UserProfile {
        return { ...UserProfile, ...profile };
    }

    async editProfile(id: string, updatedProfile: Partial<UserProfile>, callback = (profile: any, list: any) => { }) {
        await this.waitForInitialization();
        if (this.profiles[id] && id !== this.DEFAULT_PROFILE_ID) {
            const mergedProfile = { ...this.profiles[id], ...updatedProfile };
            this.profiles[id] = mergedProfile;
            this.markProfileAsModified(id);

            if (this.autoSync) {
                this.syncLocal(async () => {
                    let newProfilesList = await this.listProfiles();
                    callback(newProfilesList[id], newProfilesList);
                });
            }
        }
    }

    // Edit current profile
    editCurrentProfile(updatedProfile: Partial<UserProfile>) {
        this.editProfile(this.currentProfileId, updatedProfile);
    }

    // Mark a profile as favorite by ID
    async markAsFavorite(id: string) {
        await this.waitForInitialization();
        const profile = this.profiles[id];
        if (profile) {
            profile.favorite = true;
            this.markProfileAsModified(id);

            if (this.autoSync) {
                this.syncLocal();
            }
        }
    }

    // Remove favorite status from a profile by ID
    async removeFavorite(id: string) {
        await this.waitForInitialization();
        const profile = this.profiles[id];
        if (profile) {
            profile.favorite = false;
            this.markProfileAsModified(id);

            if (this.autoSync) {
                this.syncLocal();
            }
        }
    }

    // Switch to a profile by ID
    async switchProfile(id: string, callback: any = () => { }) {
        await this.waitForInitialization();
        if (this.profiles[id] && this.currentProfileId != id) {

            // disable all active profiles
            Object.values(this.profiles).forEach((profile, index) => {
                let id = Object.keys(this.profiles)[index];
                if (profile.status) {
                    profile.status = false;
                    profile.connected = false;
                    this.markProfileAsModified(id);
                }
            });

            // Enable the new profile
            this.profiles[id].status = true;
            this.currentProfileId = id;

            this.markProfileAsModified(id);
            
            if (this.autoSync) {
                this.syncLocal(async () => {
                    let newProfilesList = await this.listProfiles();
                    callback(newProfilesList[id], newProfilesList);
                });
            }
        }
    }

    // Get current profile
    async getCurrentProfile() {
        await this.waitForInitialization();
        return this.profiles[this.currentProfileId];
    }

    selectActiveProfileId(){
        return Object.keys(this.profiles).find(key => this.profiles[key].status === true)
    }

    // Get current profile ID
    async getCurrentProfileId() {
        await this.waitForInitialization();
        return this.currentProfileId;
    }

    retainDeletions(){

        // Filter out deleted profiles
        if(this.deletedProfiles.local.size > 0){
            for (const id of this.deletedProfiles.local) {
                delete this.profiles[id];
            }
        }
        if(this.deletedProfiles.global.size > 0){
            for (const id of this.deletedProfiles.global) {
                delete this.profiles[id];
            }
        }
    }

    // Get current profile list
    async listProfiles() {
        await this.waitForInitialization();
        
        this.retainDeletions();
        
        const profilesList = { ...this.profiles };
        return profilesList;
    }

    async exportProfiles() {
        let profilesToExport = await this.listProfiles();
        delete profilesToExport[this.DEFAULT_PROFILE_ID];

        let renumberedProfiles: Record<string, any> = {};
        Object.values(profilesToExport).forEach((profile, index) => {
            renumberedProfiles[`${index + 1}`] = profile;
        });

        return JSON.stringify(renumberedProfiles, null, 4);
    }

    async importProfiles(profilesContent: string): Promise<boolean> {
        try {
            const profilesToImport = JSON.parse(profilesContent) as Record<string, UserProfile>;
        
            // Ensure profilesToImport is an object
            if (typeof profilesToImport !== 'object' || profilesToImport === null || Array.isArray(profilesToImport)) {
                return false;
            }

            await Promise.all(Object.entries(profilesToImport).map(async ([profileId, profile]) => {
                await this.addProfile(profile, () => {}, false, true);
            }));
            
            return true;
        } catch (error) {
            return false;
        }
    }

    async downloadProfiles() {
        const jsonString = await this.exportProfiles();
    
        // Create a Blob with the JSON data
        const blob = new Blob([jsonString], { type: 'application/json' });
        
        // Create a URL for the Blob
        const url = URL.createObjectURL(blob);
        
        const currentDate = new Date().toISOString().split('T')[0];

        // Create a temporary anchor element
        const a = document.createElement('a');
        a.href = url;
        a.download = `Equalizer-Backup-${currentDate}.json`;
        
        // Append the anchor to the body, click it, and remove it
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        // Revoke the URL to free up memory
        URL.revokeObjectURL(url);
    }

    // automatically load saved changes from local storage
    private async loadLocalChanges(): Promise<void> {
        return new Promise((resolve, reject) => {
            chrome.storage.local.get("profile_changes", (data) => {
                if (chrome.runtime.lastError) {
                    reject();
                } else {
                    try {
                        const profileChanges = JSON.parse(data["profile_changes"]);

                        // Check if "profile_changes" exists in local storage
                        if (profileChanges) {
                            this.addedProfiles.local = new Set(profileChanges.added?.local || []);
                            this.addedProfiles.global = new Set(profileChanges.added?.global || []);

                            this.modifiedProfiles.local = new Set(profileChanges.modified?.local || []);
                            this.modifiedProfiles.global = new Set(profileChanges.modified?.global || []);

                            this.deletedProfiles.local = new Set(profileChanges.deleted?.local || []);
                            this.deletedProfiles.global = new Set(profileChanges.deleted?.global || []);
                        }

                        resolve();
                    } catch (error) {
                        resolve();
                    }
                }
            });
        });
    }


    // automatically save the changes to local storage
    private async saveLocalChanges(): Promise<void> {
        return new Promise((resolve) => {
            let profileChanges = JSON.stringify({
                added: {
                    local: Array.from(this.addedProfiles.local),
                    global: Array.from(this.addedProfiles.global),
                },
                modified: {
                    local: Array.from(this.modifiedProfiles.local),
                    global: Array.from(this.modifiedProfiles.global),
                },
                deleted: {
                    local: Array.from(this.deletedProfiles.local),
                    global: Array.from(this.deletedProfiles.global),
                },
            });

            chrome.storage.local.set({ ['profile_changes']: profileChanges }, () => {
                resolve();
            });
        });
    }

    // Manually sync local changes to sync storage
    syncLocal(callback = () => { }) {
        this.syncProfiles(chrome.storage.local, false, async () => {
            await this.saveLocalChanges();
            callback();
        });
    }

    // Manually sync global changes to sync storage
    syncGlobal(callback = () => { }) {
        this.syncProfiles(chrome.storage.sync, true, async () => {
            await this.saveLocalChanges();
            callback();
        });
    }

    // automatically sync every 4 mins
    scheduleSync() {
        setInterval(() => {
            this.syncGlobal();
        }, 4 * 60 * 1000);
    }

    private async syncProfiles(storage: chrome.storage.StorageArea, globalSync = false, callback: any = () => { }) {
        const promises: Promise<void>[] = [];

        let deletedProfiles = globalSync ? this.deletedProfiles.global : this.deletedProfiles.local;
        if (deletedProfiles.size > 0) {
            for (const id of deletedProfiles) {
                promises.push(this.syncRemoveProfile(id, storage, globalSync));
            }
        }

        let modifiedProfiles = globalSync ? this.modifiedProfiles.global : this.modifiedProfiles.local;
        if (modifiedProfiles.size > 0) {
            for (const id of modifiedProfiles) {
                promises.push(this.syncProfile(id, this.profiles[id], storage, globalSync));
            }
        }

        let addedProfiles = globalSync ? this.addedProfiles.global : this.addedProfiles.local;
        if (addedProfiles.size > 0) {
            for (const id of addedProfiles) {
                promises.push(this.syncProfile(id, this.profiles[id], storage, globalSync));
            }
        }

        await Promise.all(promises);
        callback();
    }

    private syncProfile(id: string, profile: UserProfile, storage: chrome.storage.StorageArea, globalSync: boolean = false): Promise<void> {
        return new Promise((resolve, reject) => {
            storage.set({ [id]: JSON.stringify(profile) }, () => {
                if (chrome.runtime.lastError) {
                    const error = chrome.runtime.lastError.message || "Unknown error";
                    reject(error);
                } else {
                    if (globalSync) {
                        this.modifiedProfiles.global.delete(id);
                        this.addedProfiles.global.delete(id);
                    } else {
                        this.modifiedProfiles.local.delete(id);
                        this.addedProfiles.local.delete(id);
                    }
                    resolve();
                }
            });
        });
    }

    private syncRemoveProfile(id: string, storage: chrome.storage.StorageArea, globalSync: boolean = false): Promise<void> {
        return new Promise((resolve, reject) => {
            storage.remove(id, () => {
                if (chrome.runtime.lastError) {
                    const error = chrome.runtime.lastError.message || "Unknown error";
                    reject(error);
                } else {
                    if (globalSync) {
                        this.deletedProfiles.global.delete(id);
                    } else {
                        this.deletedProfiles.local.delete(id);
                    }
                    resolve();
                }
            });
        });
    }

    private markProfileAsModified(id: string) {
        if (!this.addedProfiles.local.has(id)) {
            this.modifiedProfiles.local.add(id);
        }

        // global
        if (!this.addedProfiles.global.has(id)) {
            this.modifiedProfiles.global.add(id);
        }

        this.saveLocalChanges();
    }

    private markProfileAsAdded(id: string) {
        if (id != this.DEFAULT_PROFILE_ID) {
            if (!this.addedProfiles.local.has(id)) {
                this.addedProfiles.local.add(id);
            }

            // global
            if (!this.addedProfiles.global.has(id)) {
                this.addedProfiles.global.add(id);
            }

            this.saveLocalChanges();
        }
    }

    private markProfileAsDeleted(id: string) {
        if (id != this.DEFAULT_PROFILE_ID) {
            if (!this.deletedProfiles.local.has(id)) {
                this.deletedProfiles.local.add(id);
            }

            // global
            if (!this.deletedProfiles.global.has(id)) {
                this.deletedProfiles.global.add(id);
            }

            this.saveLocalChanges();
        }
    }

    async deleteProfile(id: string, callback = (profile: any, list: any) => { }) {
        await this.waitForInitialization();

        if (this.profiles[id] && id !== this.DEFAULT_PROFILE_ID) {

            this.markProfileAsDeleted(id);

            if (this.autoSync) {
                this.syncLocal(async () => {
                    let newProfilesList = await this.listProfiles();
                    callback(newProfilesList[this.currentProfileId], newProfilesList);
                });
            }
        } else {
            console.error(`Profile with ID ${id} not found.`);
        }
    }

    // Clear all profiles
    async clearProfiles() {
        await this.waitForInitialization();

        for (const id in this.profiles) {
            if (this.profiles.hasOwnProperty(id)) {
                this.markProfileAsDeleted(id);
            }
        }

        this.profiles = {}; // Reset profiles
        this.syncLocal();
        this.initDefaultProfile();
    }
}

export default ProfileManager;