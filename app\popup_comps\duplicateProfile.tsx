import Basic from '../Basic';
import promptForProfileName from "./promptForProfileName";

const duplicateProfile = async (key: string, profilesList: any, setProfilesList: any, switchAfter: boolean = false, profileManager: any) => {
    try {
        const name = await promptForProfileName(profilesList[key].name + ' 2', Basic.l('new_profile_note'));
        let newProfile = { ...profilesList[key] };
        newProfile.name = name;
        newProfile.status = false;
        newProfile.connected = false;
        await profileManager.addProfile(newProfile, (profile, latestProfilesList) => {
            setProfilesList(latestProfilesList);
        }, switchAfter);
    } catch (error) { }
};

export default duplicateProfile;