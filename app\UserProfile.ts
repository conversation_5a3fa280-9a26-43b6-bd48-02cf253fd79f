// Define the profile interface
interface UserProfile {
    name: string,
    favorite: boolean,
    status: boolean,
    connected: boolean;
    volume: number;
    base_volume: number;
    muted_at: number;
    mute: boolean;
    mono: boolean;
    compressor: {
        status: boolean;
        config: {
            threshold: number;
            attack: number;
            release: number;
            makeupGain: number;
            ratio: number;
            knee: number;
        };
    };
    chorus: {
        status: boolean;
        config: {
            frequency: number;
            delayTime: number;
            depth: number;
            spread: number;
            wet: number;
        };
    };
    spatial_audio: {
        status: boolean;
        config: {
            shape: string;
            duration: number;
        };
    };
    tuna_reverb: {
        status: boolean;
        config: {
            highCut: number;
            lowCut: number;
            dryLevel: number;
            wetLevel: number;
            level: number;
        };
    };
    tone_reverb: {
        status: boolean;
        config: {
            wet: number;
            decay: number;
            preDelay: number;
        };
    };
    noise: {
        status: boolean;
        config: {
            volume: number;
            type: string;
        };
    };
    eq: number[];
}

const UserProfile: any = {
    name: "Default profile",
    favorite: false,
    status: false,
    connected: false,
    volume: 1,
    base_volume: 1,
    muted_at: 1,
    mute: false,
    mono: false,
    compressor: {
        status: true,
        config: {
            threshold: -20,
            attack: 0,
            release: 1,
            makeupGain: 1.2,
            ratio: 10,
            knee: 19
        }
    },
    chorus: {
        status: false,
        config: {
            frequency: 0,
            delayTime: 0,
            depth: 0.7,
            spread: 180,
            wet: 0.5
        }
    },
    /*
        infinity, butterfly, hummingbird, bee, triple-knot, oval, wiggly, 
        snake, mobius-strip, helix, hypotrochoid, lissajous, figure-8, 
        flower, double-helix, diamond, concentric-circles, 
        zigzag, triangle-wave, ellipse

        added in 1.0.5: celestial-dance, vortex, spiral, heart, rose, trefoil-knot, cube
    */
    spatial_audio: {
        status: false,
        config: {
            shape: "infinity",
            duration: 15000
        }
    },
    tuna_reverb: {
        status: true,
        config: {
            highCut: 20,
            lowCut: 20,
            dryLevel: 1,
            wetLevel: 0,
            level: 1
        }
    },
    tone_reverb: {
        status: false,
        config: {
            wet: 0.7,
            decay: 2,
            preDelay: 0.05
        }
    },
    noise: {
        status: false,
        config: {
            volume: -45,
            type: "brown"
        }
    },
    eq: [20, 14, 0, 0, -6, 0, -2, -18, 16, 3] // for testing we are using loudness preset
};

export default UserProfile;