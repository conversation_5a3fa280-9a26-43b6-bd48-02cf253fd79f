
var adjustFullscreenBehavior = false;
chrome.runtime.onMessage.addListener(function (message, sender, sendResponse) {
    if (message.action === "setAdjustFullscreenBehavior") {
        adjustFullscreenBehavior = message.value ? true : false;
    }
});

document.addEventListener("fullscreenchange", async (e) => {
    //e.preventDefault();
    if (adjustFullscreenBehavior) {
        if (document.fullscreenElement) {
            chrome.runtime.sendMessage({ action: "fullscreenActivated" });
        } else {
            chrome.runtime.sendMessage({ action: "fullscreenExited" });
        }
    }
});