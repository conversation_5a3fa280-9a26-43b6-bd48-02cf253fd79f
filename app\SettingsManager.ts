class SettingsManager {
	private settings: { [key: string]: any } = {};
	private autoSync: boolean = true; // Add any other settings or methods as needed
	private isInitialized: boolean = false; // Flag to track initialization

	constructor(autoSync: boolean = true) {
		this.autoSync = autoSync;
		this.loadSettings();
	}

	// Load settings from storage
	private async loadSettings(): Promise<void> {
		return new Promise((resolve) => {
			chrome.storage.local.get(['settings'], (data: any) => {
				this.settings = JSON.parse(data as string) || {};
				resolve();
			});
		});
	}

	async waitForInitialization(): Promise<void> {
		return new Promise((resolve) => {
			const checkInitialization = () => {
				if (this.isInitialized) {
					resolve();
				} else {
					setTimeout(checkInitialization, 10); // Check again after a short delay
				}
			};
			checkInitialization();
		});
	}

	// Get a setting by key
	async getSetting(key: string) {
		await this.waitForInitialization();
		return this.settings[key];
	}

	// Set a setting by key
	async setSetting(key: string, value: any) {
		await this.waitForInitialization();
		this.settings[key] = value;
		if (this.autoSync) {
			this.syncSettings();
		}
	}

	// Clear all settings
	async clearSettings() {
		await this.waitForInitialization();

		const keys = Object.keys(this.settings);
		chrome.storage.local.remove(keys);

		this.settings = {};
		if (this.autoSync) {
			this.syncSettings();
		}
	}

	// Save settings to storage
	private syncSettings() {
		chrome.storage.local.set(this.settings);
	}
}

export default SettingsManager;
