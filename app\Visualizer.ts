class Vector3 {
  x: number;
  y: number;
  z: number;

  constructor(x: number, y: number, z: number) {
    this.x = x;
    this.y = y;
    this.z = z;
  }

  dot2(t: number, o: number): number {
    return this.x * t + this.y * o;
  }

  dot3(t: number, o: number, r: number): number {
    return this.x * t + this.y * o + this.z * r;
  }
}

namespace noise {
  let e = [
    151, 160, 137, 91, 90, 15, 131, 13, 201, 95, 96, 53, 194, 233, 7, 225, 140,
    36, 103, 30, 69, 142, 8, 99, 37, 240, 21, 10, 23, 190, 6, 148, 247, 120, 234,
    75, 0, 26, 197, 62, 94, 252, 219, 203, 117, 35, 11, 32, 57, 177, 33, 88,
    237, 149, 56, 87, 174, 20, 125, 136, 171, 168, 68, 175, 74, 165, 71, 134,
    139, 48, 27, 166, 77, 146, 158, 231, 83, 111, 229, 122, 60, 211, 133, 230,
    220, 105, 92, 41, 55, 46, 245, 40, 244, 102, 143, 54, 65, 25, 63, 161, 1,
    216, 80, 73, 209, 76, 132, 187, 208, 89, 18, 169, 200, 196, 135, 130, 116,
    188, 159, 86, 164, 100, 109, 198, 173, 186, 3, 64, 52, 217, 226, 250, 124,
    123, 5, 202, 38, 147, 118, 126, 255, 82, 85, 212, 207, 206, 59, 227, 47, 16,
    58, 17, 182, 189, 28, 42, 223, 183, 170, 213, 119, 248, 152, 2, 44, 154,
    163, 70, 221, 153, 101, 155, 167, 43, 172, 9, 129, 22, 39, 253, 19, 98, 108,
    110, 79, 113, 224, 232, 178, 185, 112, 104, 218, 246, 97, 228, 251, 34, 242,
    193, 238, 210, 144, 12, 191, 179, 162, 241, 81, 51, 145, 235, 249, 14, 239,
    107, 49, 192, 214, 31, 181, 199, 106, 157, 184, 84, 204, 176, 115, 121, 50,
    45, 127, 4, 150, 254, 138, 236, 205, 93, 222, 114, 67, 29, 24, 72, 243, 141,
    128, 195, 78, 66, 215, 61, 156, 180,
  ];

  let a: number[] = new Array(512);
  let i: Vector3[] = new Array(512);

  export function seed(t: number): void {
    if (t > 0 && t < 1) {
      t *= 65536;
    }
    t = Math.floor(t);
    if (t < 256) {
      t |= t << 8;
    }
    for (let o = 0; o < 256; o++) {
      let r;
      r = 1 & o ? e[o] ^ (255 & t) : e[o] ^ ((t >> 8) & 255);
      a[o] = a[o + 256] = r;
      i[o] = i[o + 256] = new Vector3(e[r % 12], 0, 0);
    }
  }
  seed(0);

  let d = 0.5 * (Math.sqrt(3) - 1);
  let f = (3 - Math.sqrt(3)) / 6;
  let h = 1 / 6;

  function uu(t: number): number {
    return t * t * t * (t * (6 * t - 15) + 10);
  }

  function ss(t: number, o: number, r: number): number {
    return (1 - r) * t + r * o;
  }

  export function simplex2(t: number, o: number): number {
    let r, n;
    let e = (t + o) * 0.5 * (Math.sqrt(3) - 1);
    let h = Math.floor(t + e);
    let u = Math.floor(o + e);
    let l = (h + u) * 0.5 * (3 - Math.sqrt(3));
    let w = t - h + l;
    let v = o - u + l;
    if (w > v) {
      r = 1;
      n = 0;
    } else {
      r = 0;
      n = 1;
    }
    let M = w - r + 0.5 * (3 - Math.sqrt(3));
    let c = v - n + 0.5 * (3 - Math.sqrt(3));
    let p = w - 1 + 0.5 * (3 - Math.sqrt(3));
    let y = v - 1 + 0.5 * (3 - Math.sqrt(3));
    let x = i[(h & 255) + a[(u & 255)]];
    let m = i[h + r + a[u + n]];
    let q = i[h + 1 + a[u + 1]];
    let z = 0.5 - w * w - v * v;
    let A = 0.5 - M * M - c * c;
    let b = 0.5 - p * p - y * y;
    if (z < 0) z = 0;
    if (A < 0) A = 0;
    if (b < 0) b = 0;
    return (
      70 *
      (z * z * z * z * x.dot2(w, v) +
        A * A * A * A * m.dot2(M, c) +
        b * b * b * b * q.dot2(p, y))
    );
  }

  export function simplex3(t: number, o: number, r: number): number {
    let n, e, d, f, u, s;
    let l = (t + o + r) * (1 / 3);
    let w = Math.floor(t + l);
    let v = Math.floor(o + l);
    let M = Math.floor(r + l);
    let c = (w + v + M) * h;
    let p = t - w + c;
    let y = o - v + c;
    let x = r - M + c;
    if (p >= y) {
      if (y >= x) {
        n = 1;
        e = 0;
        d = 0;
        f = 1;
        u = 1;
        s = 0;
      } else if (p >= x) {
        n = 1;
        e = 0;
        d = 0;
        f = 1;
        u = 0;
        s = 1;
      } else {
        n = 0;
        e = 0;
        d = 1;
        f = 1;
        u = 0;
        s = 1;
      }
    } else if (y < x) {
      n = 0;
      e = 0;
      d = 1;
      f = 0;
      u = 1;
      s = 1;
    } else if (p < x) {
      n = 0;
      e = 1;
      d = 0;
      f = 0;
      u = 1;
      s = 1;
    } else {
      n = 0;
      e = 1;
      d = 0;
      f = 1;
      u = 1;
      s = 0;
    }
    let m = p - n + h;
    let q = y - e + h;
    let z = x - d + h;
    let A = p - f + 2 * h;
    let b = y - u + 2 * h;
    let g = x - s + 2 * h;
    let j = p - 1 + 0.5;
    let k = y - 1 + 0.5;
    let B = x - 1 + 0.5;
    let C = i[(w &= 255) + a[(v &= 255) + a[(M &= 255)]]].dot3(t, o, r);
    let D = i[w + n + a[v + e + a[M + d]]].dot3(p, y, x);
    let E = i[w + f + a[v + u + a[M + s]]].dot3(t - 1, o - 1, r - 1);
    let F = i[w + 1 + a[v + 1 + a[M + 1]]].dot3(j, k, B);
    let G = 0.6 - p * p - y * y - x * x;
    let H = 0.6 - m * m - q * q - z * z;
    let I = 0.6 - A * A - b * b - g * g;
    let J = 0.6 - j * j - k * j - B * B;
    if (G < 0) G = 0;
    if (H < 0) H = 0;
    if (I < 0) I = 0;
    if (J < 0) J = 0;
    return (
      32 *
      (G * G * G * G * C +
        H * H * H * H * D +
        I * I * I * I * E +
        J * J * J * J * F)
    );
  }

  export function perlin2(t: number, o: number): number {
    let r = Math.floor(t);
    let n = Math.floor(o);
    t -= r;
    o -= n;
    let e = i[(r &= 255) + a[(n &= 255)]].dot2(t, o);
    let h = i[r + a[n + 1]].dot2(t, o - 1);
    let u = i[r + 1 + a[n]].dot2(t - 1, o);
    let s = i[r + 1 + a[n + 1]].dot2(t - 1, o - 1);
    let l = uu(t);
    return ss(ss(e, u, l), ss(h, s, l), uu(o));
  }

  export function perlin3(t: number, o: number, r: number): number {
    let n = Math.floor(t);
    let e = Math.floor(o);
    let d = Math.floor(r);
    t -= n;
    o -= e;
    r -= d;
    let f = i[(n &= 255) + a[(e &= 255) + a[(d &= 255)]]].dot3(t, o, r);
    let h = i[n + a[e + a[d + 1]]].dot3(t, o, r - 1);
    let l = i[n + a[e + 1 + a[d]]].dot3(t, o - 1, r);
    let w = i[n + a[e + 1 + a[d + 1]]].dot3(t, o - 1, r - 1);
    let v = i[n + 1 + a[e + a[d]]].dot3(t - 1, o, r);
    let M = i[n + 1 + a[e + a[d + 1]]].dot3(t - 1, o, r - 1);
    let c = i[n + 1 + a[e + 1 + a[d]]].dot3(t - 1, o - 1, r);
    let p = i[n + 1 + a[e + 1 + a[d + 1]]].dot3(t - 1, o - 1, r - 1);
    let y = uu(t);
    let x = uu(o);
    let m = uu(r);
    return ss(
      ss(ss(f, v, y), ss(h, M, y), m),
      ss(ss(l, c, y), ss(w, p, y), m),
      x
    );
  }
}

const visualizer_init = async function (mainContext) {
  var canvas = document.createElement("canvas") as HTMLCanvasElement;
  mainContext.ele.appendChild(canvas);

  var ctx = canvas.getContext("2d");
  var w = (ctx.canvas.width = mainContext.ele.offsetWidth);
  var h = (ctx.canvas.height = mainContext.ele.offsetHeight);

  window.onresize = function () {
    w = ctx.canvas.width = mainContext.ele.offsetWidth;
    h = ctx.canvas.height = mainContext.ele.offsetHeight;
  };

  var nrt = 0;
  var npt = 0;
  var dots = [];
  var lines = [];

  var config = {
    circleRadius: 40,
    multiplier: 40,
    colorSpeed: 20,
    hueStart: 220,
    glow: 0,
  };

  play();

  function emitDot() {
    if (dots.length > 150) {
      return;
    }
    dots.push({
      xp: w / 2,
      yp: h / 2,
      xv: Math.random() * 0.4 - 0.2,
      yv: Math.random() * 0.4 - 0.2,
      rad: Math.random() * (15 - 2) + 2,
      hue: Math.random() * 50 - 25,
    });
  }

  function emitLine() {
    if (lines.length > 50) {
      return;
    }
    lines.push({
      xp: w / 2,
      yp: h / 2,
      xv: Math.random() * 0.4 - 0.2,
      yv: Math.random() * 0.4 - 0.2,
      hue: Math.random() * 50 - 25,
    });
  }

  function clear() {
    var avg = averageFrequency();

    ctx.beginPath();
    var grd = ctx.createLinearGradient(w / 2, 0, w / 2, h);
    grd.addColorStop(
      0,
      "hsl(" + (config.hueStart + npt * config.colorSpeed) + ", 35%, 10%"
    );
    grd.addColorStop(
      1,
      "hsl(" + (config.hueStart + npt * config.colorSpeed) + ", 75%, 5%"
    );
    ctx.fillStyle = grd;
    ctx.fillRect(0, 0, w, h);
    ctx.closePath();
  }

  function drawDots() {
    var avg = averageFrequency();

    for (var i = 0; i < dots.length; i++) {
      ctx.beginPath();
      var grd = ctx.createRadialGradient(
        dots[i].xp + dots[i].rad,
        dots[i].yp + dots[i].rad,
        0,
        dots[i].xp + dots[i].rad,
        dots[i].yp + dots[i].rad,
        dots[i].rad
      );
      grd.addColorStop(
        0,
        "hsla(" +
        (config.hueStart + npt * config.colorSpeed + dots[i].hue) +
        ", 50%, 50%, " +
        avg / 400 +
        "%)"
      );
      grd.addColorStop(
        1,
        "hsla(" +
        (config.hueStart + npt * config.colorSpeed + dots[i].hue) +
        ", 50%, 50%, 0%)"
      );
      ctx.fillStyle = grd;
      ctx.fillRect(dots[i].xp, dots[i].yp, dots[i].rad * 2, dots[i].rad * 2);
      ctx.closePath();

      if (dots[i].xp > w || dots[i].xp < 0 || dots[i].yp > w || dots[i].yp < 0) {
        dots[i] = dots[dots.length - 1];
        dots.pop();
      } else {
        dots[i].xp += dots[i].xv * Math.pow(avg / 1000, 1.5);
        dots[i].yp += dots[i].yv * Math.pow(avg / 1000, 1.5);
      }
    }
  }

  function drawLines() {
    var avg = averageFrequency();
    var maxDist = 150;

    for (var i = 0; i < lines.length; i++) {
      for (var j = 0; j < lines.length; j++) {
        var proDist = 100 / maxDist;
        var opacity =
          100 -
          dist(lines[j].xp, lines[j].yp, lines[i].xp, lines[i].yp) * proDist;

        if (dist(lines[j].xp, lines[j].yp, lines[i].xp, lines[i].yp) < maxDist) {
          ctx.beginPath();
          ctx.lineWidth = 1;
          ctx.shadowBlur = 0;
          //ctx.strokeStyle = 'hsla(0,0%,100%, '+(opacity - 50)+'%)';
          ctx.strokeStyle =
            "hsla(" +
            (config.hueStart + npt * config.colorSpeed) +
            ", 50%, 50%, " +
            (opacity - 50) +
            "%)";
          ctx.moveTo(lines[i].xp, lines[i].yp);
          ctx.lineTo(lines[j].xp, lines[j].yp);
          ctx.stroke();
          ctx.closePath();
        }
      }

      if (
        lines[i].xp > w ||
        lines[i].xp < 0 ||
        lines[i].yp > w ||
        lines[i].yp < 0
      ) {
        lines[i] = lines[lines.length - 1];
        lines.pop();
      } else {
        lines[i].xp += (lines[i].xv * avg) / 500;
        lines[i].yp += (lines[i].yv * avg) / 500;
      }
    }
  }

  function drawSpectrum() {
    var noiseSpeed = averageFrequency();
    nrt += noiseSpeed / 3000000; // Rotation
    npt += noiseSpeed / 1000000; // Distortion
    var avg = 0;

    var noiseRotate = noise.perlin2(10, nrt);
    var points = Math.round(mainContext.frequencyBinCount - mainContext.frequencyBinCount / 3);
    var avgFrq = averageFrequency();

    for (var i = 0; i < points; i++) {
      avg += mainContext.frequencyData[i];
      avg = avg / points;

      var x1 =
        w / 2 +
        (config.circleRadius + avgFrq / 4 / points) *
        Math.cos(-Math.PI / 2 + (2 * Math.PI * i) / points + noiseRotate);
      var y1 =
        h / 2 +
        (config.circleRadius + avgFrq / 4 / points) *
        Math.sin(-Math.PI / 2 + (2 * Math.PI * i) / points + noiseRotate);
      var x2 =
        w / 2 +
        (config.circleRadius + avgFrq / 4 / points + avg * config.multiplier) *
        Math.cos(-Math.PI / 2 + (2 * Math.PI * i) / points + noiseRotate);
      var y2 =
        h / 2 +
        (config.circleRadius + avgFrq / 4 / points + avg * config.multiplier) *
        Math.sin(-Math.PI / 2 + (2 * Math.PI * i) / points + noiseRotate);
      var x3 =
        w / 2 +
        (config.circleRadius +
          avgFrq / 4 / points +
          Math.pow(avg * config.multiplier * 0.09, 2)) *
        Math.cos(-Math.PI / 2 + (2 * Math.PI * i) / points + noiseRotate);
      var y3 =
        h / 2 +
        (config.circleRadius +
          avgFrq / 4 / points +
          Math.pow(avg * config.multiplier * 0.09, 2)) *
        Math.sin(-Math.PI / 2 + (2 * Math.PI * i) / points + noiseRotate);
      var nd1 = noise.simplex2(y1 / 100, npt) * 10;

      ctx.beginPath();
      ctx.lineCap = "round";
      ctx.shadowBlur = config.glow;
      ctx.lineWidth = 1;
      ctx.strokeStyle =
        "hsla(" +
        (config.hueStart + npt * config.colorSpeed) +
        ", 50%, " +
        (20 + Math.pow(avg * 3, 2)) +
        "%, 100%)";
      ctx.shadowColor =
        "hsla(" +
        (config.hueStart + npt * config.colorSpeed) +
        ", 50%, " +
        (20 + Math.pow(avg * 3, 2)) +
        "%, 100%)";
      ctx.moveTo(x1 + nd1, y1 + nd1);
      ctx.lineTo(x2 + nd1, y2 + nd1);
      ctx.stroke();
      ctx.closePath();

      ctx.beginPath();
      ctx.lineCap = "round";
      ctx.shadowBlur = config.glow;
      ctx.lineWidth = 4;
      ctx.strokeStyle =
        "hsla(" +
        (config.hueStart + npt * config.colorSpeed) +
        ", 50%, " +
        (30 + Math.pow(avg * 3, 2)) +
        "%, 100%)";
      ctx.shadowColor =
        "hsla(" +
        (config.hueStart + npt * config.colorSpeed) +
        ", 50%, " +
        (30 + Math.pow(avg * 3, 2)) +
        "%, 100%)";
      ctx.moveTo(x1 + nd1, y1 + nd1);
      ctx.lineTo(x3 + nd1, y3 + nd1);
      ctx.stroke();
      ctx.closePath();
    }
  }

  function render() {
    //if (!playing) return;
    clear();
    drawDots();
    drawSpectrum();
    drawLines();
    requestAnimationFrame(render);
  }


  function play() {
    var dotEmitter = setInterval(emitDot, 50);
    var lineEmitter = setInterval(emitLine, 100);
    render();
  }

  function lerp(x1, x2, n) {
    return x1 + (x2 - x1) * n;
  }

  function dist(x1, y1, x2, y2) {
    var a = x1 - x2;
    var b = y1 - y2;
    return Math.sqrt(a * a + b * b);
  }

  function averageFrequency() {
    var avg = 0;
    for (var i = 0; i < mainContext.frequencyData.length; i++) {
      avg += mainContext.frequencyData[i];
    }
    return avg;
  }

}

const Visualizer = {
  items: [],
  init: async function(tabId: string, ele: any, frequencyData: any, frequencyBinCount: number){
    Visualizer.items[tabId] = {};
    Visualizer.items[tabId].tabId = tabId;
    Visualizer.items[tabId].frequencyData = frequencyData;
    Visualizer.items[tabId].frequencyBinCount = frequencyBinCount;
    Visualizer.items[tabId].ele = ele;
    visualizer_init(Visualizer.items[tabId]);
  },
  update: async function(tabId: string, ele: any, frequencyData: any, frequencyBinCount: number){
    if(Visualizer.items[tabId]){
      Visualizer.items[tabId].frequencyData = frequencyData;
      Visualizer.items[tabId].frequencyBinCount = frequencyBinCount;
    } else {
      Visualizer.init(tabId, ele, frequencyData, frequencyBinCount);
    }
  }
}

export { Visualizer };
