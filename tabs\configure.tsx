import Basic from '../app/Basic';
import React, { useState, useEffect } from 'react';
import QRCode from 'qrcode';
import { CssVarsProvider } from '@mui/joy/styles';
import theme from '../app/Theme';
import Sheet from '@mui/joy/Sheet';
import Box from '@mui/joy/Box';
import "~./app/css/configure.css";
import svg_icon from 'url:assets/icon.svg';
import Button from '@mui/joy/Button';
import Card from '@mui/joy/Card';
import CardActions from '@mui/joy/CardActions';
import Chip from '@mui/joy/Chip';
import Divider from '@mui/joy/Divider';
import List from '@mui/joy/List';
import ListItem from '@mui/joy/ListItem';
import ListItemDecorator from '@mui/joy/ListItemDecorator';
import Typography from '@mui/joy/Typography';
import Check from '@mui/icons-material/Check';
import InfoOutlined from '@mui/icons-material/InfoOutlined';
import LoyaltyIcon from '@mui/icons-material/Loyalty';
import KeyboardArrowRight from '@mui/icons-material/KeyboardArrowRight';
import KeyboardArrowLeft from '@mui/icons-material/KeyboardArrowLeft';
import Container from '@mui/joy/Container';
import Grid from '@mui/joy/Grid';
import Link from '@mui/joy/Link';
import { Transition } from 'react-transition-group';
import Modal from '@mui/joy/Modal';
import ModalDialog from '@mui/joy/ModalDialog';
import DialogTitle from '@mui/joy/DialogTitle';
import DialogContent from '@mui/joy/DialogContent';
import LoveIcon from '@mui/icons-material/Favorite';
import Alert from '@mui/joy/Alert';
import CardContent from '@mui/joy/CardContent';
import AspectRatio from '@mui/joy/AspectRatio';
import FormControl from '@mui/joy/FormControl';
import FormLabel, { formLabelClasses } from '@mui/joy/FormLabel';
import Input from '@mui/joy/Input';
import Stack from '@mui/joy/Stack';
import LaunchIcon from '@mui/icons-material/Launch';
import VerifiedIcon from '@mui/icons-material/Verified';

function LegendaryModal({ setMembership, membership, open, setOpen }) {

    const [loading, setLoading] = useState(false);
    const [alertMsg, setAlertMsg] = useState('');

    function activateLegendary(){
        setLoading(true);
        setTimeout(() => {
            setLoading(false);
            Basic.activateLegendary(() => {
                setAlertMsg('sub_active');
                setTimeout(() => {
                    setMembership('free');
                }, 500);
            });
        }, 1000);
    }


    return (
        <React.Fragment>
            <Transition in={open} timeout={100}>
                {(state: string) => (
                    <Modal
                        dir={Basic.l('extensionDirection')} 
                        keepMounted
                        open={!['exited', 'exiting'].includes(state)}
                        onClose={() => setOpen(false)}
                        slotProps={{
                            backdrop: {
                                sx: {
                                    opacity: 0,
                                    backdropFilter: 'none',
                                    transition: `opacity 200ms, backdrop-filter 200ms`,
                                    ...{
                                        entering: { opacity: 1, backdropFilter: 'blur(8px)' },
                                        entered: { opacity: 1, backdropFilter: 'blur(8px)' },
                                    }[state],
                                },
                            },
                        }}
                        sx={{
                            visibility: state === 'exited' ? 'hidden' : 'visible',
                        }}
                    >
                        <ModalDialog
                            sx={{
                                overflow: 'hidden',
                                opacity: 0,
                                transition: `opacity 300ms`,
                                ...{
                                    entering: { opacity: 1 },
                                    entered: { opacity: 1 },
                                }[state],
                            }}
                        >
                            <DialogTitle>{Basic.l('legendary_supporter')}</DialogTitle>
                            <DialogContent sx={{ overflow: 'hidden' }}>
                                {(membership == 'free') && (
                                    <Alert
                                        size="lg"
                                        color="success"
                                        variant="soft"
                                        invertedColors
                                        startDecorator={
                                        <AspectRatio
                                            variant="solid"
                                            ratio="1"
                                            sx={{
                                            minWidth: 40,
                                            borderRadius: '50%',
                                            boxShadow: '0 2px 12px 0 rgb(0 0 0/0.2)',
                                            }}
                                        >
                                            <div>
                                            <VerifiedIcon />
                                            </div>
                                        </AspectRatio>
                                        }
                                        sx={{ mt: 1, maxWidth: '400px', alignItems: 'flex-start', overflow: 'hidden' }}
                                    >
                                        <div>
                                            <Typography level="title-lg">{Basic.l('active')}</Typography>
                                            <Typography level="body-sm">
                                                {Basic.l('legendary_experience')}
                                            </Typography>
                                        </div>
                                    </Alert>
                                )}

                                {(membership != 'free') && (
                                    <Box sx={{ mt: 1 }}>
                                        {(['sub_active'].includes(alertMsg.toLowerCase())) && (
                                            <Alert sx={{ px: 1, py: 0.5, mb: 1 }} variant="soft" color="success" size="sm">
                                                {Basic.l(alertMsg.toLowerCase())}
                                            </Alert>
                                        )}
                                        <Typography sx={{ mb:1 }} level="body-sm">
                                            {Basic.l('legendary_experience')}
                                        </Typography>
                                        <Button
                                            loading={loading}
                                            variant="soft"
                                            color="neutral"
                                            fullWidth
                                            onClick={() => activateLegendary() }
                                        >
                                            <Check sx={{ px: 1 }} /> {Basic.l('activate')}
                                        </Button>
                                    </Box>
                                )}
                            </DialogContent>
                        </ModalDialog>
                    </Modal>
                )}
            </Transition>
        </React.Fragment>
    );
}

function PremiumModal({ setMembership, membership, open, setOpen }) {

    const [qrDataUrl, setQrDataUrl] = useState('');
    const [email, setEmail] = useState('');
    const [alertMsg, setAlertMsg] = useState('');
    const [loading, setLoading] = useState(false);

    const generateQR = async (text) => {
        try {
            const imageDataUrl = await QRCode.toDataURL(text);
            setQrDataUrl(imageDataUrl);
        } catch (error) { }
    };

    function activateMembership(){
        setLoading(true);
        Basic.activateMembership(email, (status, response) => {
            setLoading(false);
            if(status){
                if(response['status'] == 'success'){
                    setAlertMsg(response['result']['status']);
                    setTimeout(() => {
                        setMembership('premium');
                    }, 500);
                } else if (response['status'] == 'error'){
                    setAlertMsg(response['result']['status'] || response['result']);
                } else {
                    setAlertMsg('INTERNAL_ERROR');
                }
            } else {
                setAlertMsg(response);
            }
        });
    }

    function activateLegendary(){
        setLoading(true);
        setTimeout(() => {
            setLoading(false);
            Basic.activateLegendary(() => {
                setAlertMsg('');
                setMembership('free');
            });
        }, 1000);
    }
    
    useEffect(() => {
        generateQR(Basic.subscribeURL()); // Generate QR code when component mounts
        Basic.checkMembership();
    }, []);

    return (
        <React.Fragment>
            <Transition in={open} timeout={100}>
                {(state: string) => (
                    <Modal
                        dir={Basic.l('extensionDirection')} 
                        keepMounted
                        open={!['exited', 'exiting'].includes(state)}
                        onClose={() => setOpen(false)}
                        slotProps={{
                            backdrop: {
                                sx: {
                                    opacity: 0,
                                    backdropFilter: 'none',
                                    transition: `opacity 200ms, backdrop-filter 200ms`,
                                    ...{
                                        entering: { opacity: 1, backdropFilter: 'blur(8px)' },
                                        entered: { opacity: 1, backdropFilter: 'blur(8px)' },
                                    }[state],
                                },
                            },
                        }}
                        sx={{
                            visibility: state === 'exited' ? 'hidden' : 'visible',
                        }}
                    >
                        <ModalDialog
                            sx={{
                                overflow: 'hidden',
                                opacity: 0,
                                transition: `opacity 300ms`,
                                ...{
                                    entering: { opacity: 1 },
                                    entered: { opacity: 1 },
                                }[state],
                            }}
                        >
                            <DialogTitle>{Basic.l('premium_supporter')}</DialogTitle>
                            <DialogContent sx={{ overflow: 'hidden' }}>
                                {(membership == 'premium') && (
                                    <Box>
                                        <Alert
                                            size="lg"
                                            color="success"
                                            variant="soft"
                                            invertedColors
                                            startDecorator={
                                            <AspectRatio
                                                variant="solid"
                                                ratio="1"
                                                sx={{
                                                minWidth: 40,
                                                borderRadius: '50%',
                                                boxShadow: '0 2px 12px 0 rgb(0 0 0/0.2)',
                                                }}
                                            >
                                                <div>
                                                <VerifiedIcon />
                                                </div>
                                            </AspectRatio>
                                            }
                                            sx={{ mt: 1, maxWidth: '400px', alignItems: 'flex-start', overflow: 'hidden' }}
                                        >
                                            <div>
                                                <Typography level="title-lg">{Basic.l('active')}</Typography>
                                                <Typography level="body-sm">
                                                    {Basic.l('premium_experience')}
                                                </Typography>
                                            </div>
                                        </Alert>
                                        <Box sx={{ mt: 1.5 }}>
                                            {(['sub_active'].includes(alertMsg.toLowerCase())) && (
                                                <Alert sx={{ px: 1, py: 0.5, mb: 1 }} variant="soft" color="success" size="sm">
                                                    {Basic.l(alertMsg.toLowerCase())}
                                                </Alert>
                                            )}
                                            <Button
                                                loading={loading}
                                                variant="soft"
                                                color="neutral"
                                                fullWidth
                                                onClick={() => activateLegendary() }
                                            >
                                                {Basic.l('cancel_subscription')}
                                            </Button>
                                        </Box>
                                    </Box>
                                )}

                                {(membership != 'premium') && (
                                    <Box
                                        component="main"
                                        sx={{
                                            my: 'auto',
                                            py: 2,
                                            pb: 2,
                                            display: 'flex',
                                            flexDirection: 'column',
                                            gap: 2,
                                            width: 400,
                                            maxWidth: '100%',
                                            mx: 'auto',
                                            borderRadius: 'sm',
                                            '& form': {
                                                display: 'flex',
                                                flexDirection: 'column',
                                                gap: 2,
                                            },
                                            [`& .${formLabelClasses.asterisk}`]: {
                                                visibility: 'hidden',
                                            },
                                        }}
                                    >
                                        <Stack gap={1} sx={{ mb: 1 }}>
                                            <Button
                                                variant="soft"
                                                color="neutral"
                                                fullWidth
                                                onClick={() => {
                                                    window.open(Basic.subscribeURL(), '_blank');
                                                }}
                                            >
                                                <LaunchIcon sx={{ px: 1 }} /> {Basic.l('click_to_donate')}
                                                <Typography level="body-sm" sx={{ opacity: 0.8, mx: 1 }}>{ Basic.premium_cost() + '$' }</Typography>
                                            </Button>
                                            <Card
                                                variant="outlined"
                                                orientation="horizontal"
                                                sx={{
                                                    mt: 0.5,
                                                    p: 1,
                                                    '&:hover': { backgroundColor: 'neutral.outlinedHoverBorder' },
                                                }}
                                            >
                                                <AspectRatio ratio="1" sx={{ width: 90 }}>
                                                    <img
                                                        src={qrDataUrl}
                                                        alt="SCAN"
                                                    />
                                                </AspectRatio>
                                                <CardContent>
                                                    <Typography level="body-xs" aria-describedby="card-description" mb={0.6}>
                                                    {Basic.l('donation_hint')}
                                                    </Typography>
                                                    <Divider />
                                                    <Chip
                                                        variant="soft"
                                                        color="neutral"
                                                        size="sm"
                                                        sx={{ mt: 0.6, pointerEvents: 'none' }}
                                                    >
                                                        {Basic.l('lifetime_access_hint', [Basic.premium_lifetime_cost()+'$'])}
                                                    </Chip>
                                                </CardContent>
                                            </Card>
                                        </Stack>
                                        <Divider
                                            sx={(theme) => ({
                                                [theme.getColorSchemeSelector('light')]: {
                                                    color: { xs: '#FFF', md: 'text.tertiary' },
                                                    '--Divider-lineColor': {
                                                        xs: '#FFF',
                                                        md: 'var(--joy-palette-divider)',
                                                    },
                                                },
                                            })}
                                        >
                                            {Basic.l('after_donation')}
                                        </Divider>
                                        <Stack gap={1} sx={{ mt: 0 }}>
                                            <Box>
                                                {(['sub_active'].includes(alertMsg.toLowerCase())) && (
                                                    <Alert sx={{ px: 1, py: 0.5, mb: 1 }} variant="soft" color="success" size="sm">
                                                        {Basic.l(alertMsg.toLowerCase())}
                                                    </Alert>
                                                )}
                                                {(['sub_expired', 'sub_notfound', 'product_notfound', 'internal_error'].includes(alertMsg.toLowerCase())) && (
                                                    <Alert sx={{ px: 1, py: 0.5, mb: 1 }}  variant="soft" color="danger" size="sm">
                                                        {Basic.l(alertMsg.toLowerCase())}
                                                    </Alert>
                                                )}
                                                {(!['sub_active', 'sub_expired', 'sub_notfound', 'product_notfound', 'internal_error'].includes(alertMsg.toLowerCase()) && alertMsg != '') && (
                                                    <Alert sx={{ px: 1, py: 0.5, mb: 1 }} variant="soft" color="danger" size="sm">
                                                        {alertMsg}
                                                    </Alert>
                                                )}
                                                <FormControl required sx={{ 
                                                    mb: 1.5
                                                }}>
                                                    <FormLabel>{Basic.l('email_key')}</FormLabel>
                                                    <Input onChange={(event) => {
                                                            setEmail(event.target.value);
                                                        }}
                                                        onKeyDown={(event) => {
                                                            if (event.key === 'Enter') {
                                                                event.preventDefault(); // Prevent the default Enter key behavior (e.g., submitting a form)
                                                                activateMembership();
                                                            }
                                                        }}
                                                        placeholder={Basic.l('email_key_placeholder')+"..."} />
                                                </FormControl>
                                                <Button
                                                    loading={loading}
                                                    variant="soft"
                                                    color="neutral"
                                                    fullWidth
                                                    onClick={() => activateMembership() }
                                                >
                                                    <Check sx={{ px: 1 }} /> {Basic.l('verify_donation')}
                                                </Button>
                                            </Box>
                                        </Stack>
                                    </Box>
                                )}

                            </DialogContent>
                        </ModalDialog>
                    </Modal>
                )}
            </Transition>
        </React.Fragment>
    );
}


export default function Configure() {
    const [legendaryModal, openLegendaryModal] = useState<boolean>(false);
    const [premiumModal, openPremiumModal] = useState<boolean>(false);
    const [membership, setMembership] = useState('');

    useEffect(() => {
        document.title = Basic.l('extensionName');

        async function fetchMembershipType() {
            const type = await Basic.getMembershipType();
            setMembership(type);
        }

        fetchMembershipType();
    }, []);

    function MembershipCard({ status, children }) {
        if (status == 'active') {
            return (
                <Card size="lg" variant="outlined">
                    {children}
                </Card>
            );
        } else {
            return (
                <Card
                    size="lg"
                    variant="solid"
                    color="neutral"
                    invertedColors
                    sx={{ bgcolor: 'neutral.900' }}
                >
                    {children}
                </Card>
            );
        }
    }
    function MembershipButton({ status, membership, children }) {
        if (status == 'active') {
            return (
                <Button
                    variant='soft'
                    color='neutral'
                    onClick={() => {
                        if (membership == 'premium') {
                            openPremiumModal(true);
                        } else {
                            openLegendaryModal(true);
                        }
                    }}
                >
                    {membership == 'premium' ? Basic.l('premium_supporter') : Basic.l('legendary_supporter')}
                    <Check sx={{ color: Basic.colors.green, px: 1 }} />
                </Button>
            );
        } else {
            return (
                <Button
                    onClick={() => {
                        if (membership == 'premium') {
                            openPremiumModal(true);
                        } else {
                            openLegendaryModal(true);
                        }
                    }}
                >
                    {children}
                    {(Basic.l('extensionDirection') != 'rtl') && (
                        <KeyboardArrowRight sx={{ px: 1 }} />
                    )}
                    {(Basic.l('extensionDirection') == 'rtl') && (
                        <KeyboardArrowLeft sx={{ px: 1 }} />
                    )}
                </Button>
            );
        }
    }

    return (
        <CssVarsProvider defaultMode="system" theme={theme} >
            <div dir={Basic.l('extensionDirection')} style={{ margin: "0px", padding: "0px" }} className="main_configure no-select">
                <Sheet id="configure_wrapper">
                    <Box
                        sx={{
                            height: '100vh',
                            overflowY: 'auto',
                            scrollSnapType: 'y mandatory',
                            '& > div': {
                                scrollSnapAlign: 'start',
                            },
                        }}
                    >
                        <Container maxWidth="md">
                            <Grid
                                spacing={1}
                                container
                                direction="row"
                                justifyContent="center"
                                alignItems="flex-start"
                            >
                                <Grid xs={12} sx={{ mt: 10 }}>
                                    <Typography component={'span'} startDecorator={<img
                                        src={svg_icon}
                                        style={{ 'width': '3.1rem', 'height': '3.1rem', 'marginRight': Basic.l('extensionDirection') == 'rtl' ? '0px' : '5px', 'marginLeft': Basic.l('extensionDirection') == 'rtl' ? '5px' : '0px' }}
                                    />} level="body-lg" >
                                        <Typography level="h4" noWrap>
                                            {Basic.l('extensionName')}
                                            <Divider sx={{ opacity: 0 }} />
                                            <Typography level="body-sm" textColor="text.tertiary" noWrap sx={{
                                                opacity: 0.9,
                                                fontSize: "sm",
                                                lineHeight: "xs",
                                                fontWeight: "sm",
                                                display: "block"
                                            }} >
                                                {Basic.l('extensionTagline')}
                                            </Typography>
                                        </Typography>
                                    </Typography>
                                    <Divider sx={{ mt: 1.3, mb: 0.4, opacity: 0.6 }} />
                                </Grid>
                                <Grid xs={12} md={6} lg={6}>
                                    <MembershipCard status={membership == 'free' ? 'active' : 'inactive'} >
                                        <Chip size="sm" sx={{ px: 1, opacity: 0.8 }} variant="outlined" color="neutral">
                                            {Basic.l('free').toUpperCase()}
                                        </Chip>
                                        <Typography level="h2">{Basic.l('legendary')}</Typography>
                                        <Divider sx={{ opacity: 0.3 }} inset="none" />
                                        <List size="sm" sx={{ mx: 'calc(-1 * var(--ListItem-paddingX))' }}>
                                            <ListItem>
                                                <ListItemDecorator>
                                                    <Check />
                                                </ListItemDecorator>
                                                {Basic.l('legendary_experience')}
                                            </ListItem>
                                            <ListItem>
                                                <ListItemDecorator>
                                                    <Check />
                                                </ListItemDecorator>
                                                {Basic.l('unlimited_devices')}
                                            </ListItem>
                                            <ListItem>
                                                <ListItemDecorator>
                                                    <Check />
                                                </ListItemDecorator>
                                                {Basic.l('continuous_updates')}
                                            </ListItem>
                                            <ListItem>
                                                <ListItemDecorator>
                                                    <InfoOutlined />
                                                </ListItemDecorator>
                                                {Basic.l('affiliate_ad')}
                                            </ListItem>
                                        </List>
                                        <Divider sx={{ opacity: 0.3 }} inset="none" />
                                        <CardActions>
                                            <Typography level="title-lg" sx={{ mr: Basic.l('extensionDirection') == 'rtl' ? '' : 'auto', ml: Basic.l('extensionDirection') == 'rtl' ? 'auto' : '' }}>
                                                {Basic.l('free') + ' '}
                                                <Typography fontSize="sm" sx={{ opacity: 0.6 }} textColor="text.tertiary">
                                                    / {Basic.l('forever')}
                                                </Typography>
                                            </Typography>
                                            <MembershipButton membership="free" status={membership == 'free' ? 'active' : 'inactive'} >
                                                {Basic.l('select')}
                                            </MembershipButton>
                                        </CardActions>
                                    </MembershipCard>
                                </Grid>
                                <Grid xs={12} md={6} lg={6}>
                                    <MembershipCard status={membership == 'premium' ? 'active' : 'inactive'}>
                                        <Chip size="sm" sx={{ px: 1, opacity: 0.8 }} variant="outlined">
                                            {Basic.l('paid').toUpperCase()}
                                        </Chip>
                                        <Typography level="h2">{Basic.l('premium')}</Typography>
                                        <Divider sx={{ opacity: 0.3 }} inset="none" />
                                        <List size="sm" sx={{ mx: 'calc(-1 * var(--ListItem-paddingX))' }}>
                                            <ListItem>
                                                <ListItemDecorator>
                                                    <Check />
                                                </ListItemDecorator>
                                                {Basic.l('premium_experience')}
                                            </ListItem>
                                            <ListItem>
                                                <ListItemDecorator>
                                                    <Check />
                                                </ListItemDecorator>
                                                {Basic.l('unlimited_devices')}
                                            </ListItem>
                                            <ListItem>
                                                <ListItemDecorator>
                                                    <Check />
                                                </ListItemDecorator>
                                                {Basic.l('continuous_updates')}
                                            </ListItem>
                                            <ListItem>
                                                <ListItemDecorator>
                                                    <LoyaltyIcon />
                                                </ListItemDecorator>
                                                {Basic.l('without_Ads')}
                                            </ListItem>
                                        </List>
                                        <Divider sx={{ opacity: 0.3 }} inset="none" />
                                        <CardActions>
                                            <Typography level="title-lg" sx={{ mr: Basic.l('extensionDirection') == 'rtl' ? '' : 'auto', ml: Basic.l('extensionDirection') == 'rtl' ? 'auto' : '' }}>
                                                {Basic.premium_cost() + '$ '}
                                                <Typography fontSize="sm" sx={{ opacity: 0.6 }} textColor="text.tertiary">
                                                    / {Basic.l('yearly_donation')}
                                                </Typography>
                                            </Typography>
                                            <MembershipButton membership="premium" status={membership == 'premium' ? 'active' : 'inactive'} >
                                                {Basic.l('continue_premium')}
                                            </MembershipButton>
                                        </CardActions>
                                    </MembershipCard>
                                </Grid>
                                <Grid xs={12} sx={{ mt: 0.5 }}>
                                    <Alert size="sm" startDecorator={<LoveIcon />}>
                                        {Basic.l('supporters_thank_you')}
                                    </Alert>
                                    <Typography textColor="text.tertiary" startDecorator={<InfoOutlined />} sx={{ mt: 1.2, fontWeight: "sm", fontSize: '12px', opacity: 0.9 }}>
                                        {Basic.l('terms_privacy_notice')}
                                        <Link
                                            sx={{ ml: Basic.l('extensionDirection') == 'rtl' ? 0 : 0.4, mr: Basic.l('extensionDirection') == 'rtl' ? 0.4 : 0 }}
                                            href={Basic.privacy_url()}
                                            target='_blank'
                                        >{' ' + Basic.l('privacy_policy').toLowerCase()}</Link>,
                                        <Link
                                            sx={{ ml: Basic.l('extensionDirection') == 'rtl' ? 0 : 0.4, mr: Basic.l('extensionDirection') == 'rtl' ? 0.4 : 0 }}
                                            href={Basic.terms_url()}
                                            target='_blank'
                                        >{' ' + Basic.l('terms_of_service').toLowerCase()}</Link>.
                                    </Typography>
                                </Grid>
                            </Grid>
                        </Container>
                    </Box>
                    <LegendaryModal setMembership={setMembership} membership={membership} open={legendaryModal} setOpen={openLegendaryModal} />
                    <PremiumModal setMembership={setMembership} membership={membership} open={premiumModal} setOpen={openPremiumModal} />
                </Sheet>
            </div>
        </CssVarsProvider>
    );
}
