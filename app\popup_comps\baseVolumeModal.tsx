import Basic from '../Basic';
import React, { useCallback, useEffect } from 'react';
import Box from '@mui/joy/Box';
import Slider from '@mui/joy/Slider';
import Grid from '@mui/joy/Grid';
import Tooltip from '@mui/joy/Tooltip';
import IconButton from '@mui/joy/IconButton';
import Typography from '@mui/joy/Typography';
import { Transition } from 'react-transition-group';
import Modal from '@mui/joy/Modal';
import ModalDialog from '@mui/joy/ModalDialog';
import DialogTitle from '@mui/joy/DialogTitle';
import DialogContent from '@mui/joy/DialogContent';

// Icons
import ResetIcon from '@mui/icons-material/RotateLeft';

const BaseVolumeModal =function ({ eq_knobs, open, setOpen, baseVolume, setBaseVolume, callback = () => { }, sendStatus, sendMessage, tabSettings, currentTab }) {

    const handleSliderChange = useCallback(async (id: number, value: number) => {
        eq_knobs.value = value; // Update the global values
        setBaseVolume(value);

        if (currentTab != null && sendStatus && tabSettings.connected) {
            sendMessage({
                type: eq_knobs.event_name,
                target: 'offscreen',
                tabid: currentTab.id,
                data: (eq_knobs.value / 100)
            });
        }
    }, [currentTab, eq_knobs, sendStatus, sendMessage, tabSettings, setBaseVolume]);

    const resetVolume = useCallback(() => {
        setBaseVolume(100);
        handleSliderChange(1, 100);
    }, [handleSliderChange, setBaseVolume]);

    const knobParams = useCallback((params, index = 0) => {
        return Array.isArray(params) ? params[index] : params;
    }, []);

    useEffect(() => {
        eq_knobs.value = baseVolume;
        if (sendStatus && tabSettings.connected) {
            sendMessage({
                type: eq_knobs.event_name,
                target: 'offscreen',
                tabid: currentTab.id,
                data: (eq_knobs.value / 100)
            });
        }
    }, [baseVolume, eq_knobs, sendMessage, sendStatus, tabSettings]);

    return (
        <React.Fragment>
            <Transition in={open} timeout={100}>
                {(state: string) => (
                    <Modal
                        keepMounted
                        open={!['exited', 'exiting'].includes(state)}
                        onClose={() => setOpen(false)}
                        slotProps={{
                            backdrop: {
                                sx: {
                                    opacity: 0,
                                    backdropFilter: 'none',
                                    transition: `opacity 200ms, backdrop-filter 200ms`,
                                    ...{
                                        entering: { opacity: 1, backdropFilter: 'blur(8px)' },
                                        entered: { opacity: 1, backdropFilter: 'blur(8px)' },
                                    }[state],
                                },
                            },
                        }}
                        sx={{
                            visibility: state === 'exited' ? 'hidden' : 'visible',
                        }}
                    >
                        <ModalDialog
                            sx={{
                                opacity: 0,
                                transition: `opacity 300ms`,
                                ...{
                                    entering: { opacity: 1 },
                                    entered: { opacity: 1 },
                                }[state],
                            }}
                        >
                            <DialogTitle>{Basic.l('base_volume')}</DialogTitle>
                            <DialogContent>
                                <Typography level="body-xs">{Basic.l('base_volume_subtitle')}</Typography>
                                <Box sx={{ width: '300px', margin: 0, pr: Basic.l('extensionDirection') == 'rtl' ? 0 : 3, pl: Basic.l('extensionDirection') == 'rtl' ? 3 : 0, py: 2 }}>
                                    <Grid container spacing={1} sx={{ flexGrow: 1 }}>
                                        <Grid xs="auto"
                                            display="flex"
                                            justifyContent="center"
                                            alignItems="center"
                                        >
                                            <Typography level="body-xs">
                                                {eq_knobs.name}
                                            </Typography>
                                        </Grid>
                                        <Grid xs
                                            display="flex"
                                            justifyContent="center"
                                            alignItems="center"
                                        >
                                            <Tooltip title={Basic.l('reset_default_volume')} color="neutral" size="sm" placement="top" variant="soft">
                                                <IconButton
                                                    onClick={resetVolume}
                                                    sx={{ ml: Basic.l('extensionDirection') == 'rtl' ? 1 : 0, mr: Basic.l('extensionDirection') == 'rtl' ? 0 : 1 }} >
                                                    <ResetIcon />
                                                </IconButton>
                                            </Tooltip>
                                            <Slider
                                                color="primary"
                                                variant="solid"
                                                min={knobParams(eq_knobs.min)}
                                                max={knobParams(eq_knobs.max)}
                                                step={knobParams(eq_knobs.step)}
                                                value={baseVolume}
                                                orientation='horizontal'
                                                aria-label="EQ"
                                                valueLabelDisplay="auto"
                                                sx={{
                                                    "--Slider-trackSize": "10px",
                                                    "--Slider-thumbSize": "24px",
                                                    "--Slider-thumbWidth": "24px",
                                                    "--Slider-thumbColor": "#2eff5a !important",
                                                    "--Slider-thumbBackground": "#2eff5a",
                                                    "--Slider-valueLabelArrowSize": "10px",
                                                    pb: 0
                                                }}
                                                onChange={handleSliderChange}
                                            />
                                        </Grid>
                                    </Grid>
                                </Box>
                            </DialogContent>
                        </ModalDialog>
                    </Modal>
                )}
            </Transition>
        </React.Fragment>
    );
}

export default BaseVolumeModal;