interface QueueItem {
    type: string;
    data: any;
}

const messageQueue: QueueItem[] = [];
let isProcessing = false;
const THROTTLE_DELAY = 10; // Delay between processing queue items in milliseconds
const COLLECTION_DELAY = 50; // Delay for collecting changes before queueing in milliseconds

const pendingMessages: { [key: string]: QueueItem } = {};
const collectionTimers: { [key: string]: any } = {};

function processQueue() {
    if (messageQueue.length === 0) {
        isProcessing = false;
        return;
    }

    isProcessing = true;
    const item = messageQueue.shift();
    if (item) {
        chrome.runtime.sendMessage(item.data);
    }

    setTimeout(() => {
        processQueue();
    }, THROTTLE_DELAY);
}

function queueMessage(type: string) {
    if (pendingMessages[type]) {
        messageQueue.push(pendingMessages[type]);
        delete pendingMessages[type];
        delete collectionTimers[type];

        if (!isProcessing) {
            processQueue();
        }
    }
}

function sendMessage(data: any) {

    // Update the pending message for this type
    pendingMessages[data.type] = { type: data.type, data };

    // Clear any existing timer for this message type
    if (collectionTimers[data.type]) {
        clearTimeout(collectionTimers[data.type]);
    }

    // Set a new timer for this message type
    collectionTimers[data.type] = setTimeout(() => queueMessage(data.type), COLLECTION_DELAY);
}

// Export the sendMessage function
export { sendMessage };