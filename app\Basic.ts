import axios from 'axios';

const Basic = {
    colors: {
        green: '#2eff5a',
        black: '#000000',
        red: '#f73333',
        blue: '#29b6f6',
    },
    l: function (name: string, translation_vars = []) {
        return chrome.i18n.getMessage(name, translation_vars);
    },
    version: function () {
        return process.env.PLASMO_PUBLIC_VERSION;
    },
    premium_cost: function () {
        return process.env.PLASMO_PUBLIC_PREMIUM;
    },
    premium_lifetime_cost: function () {
        return process.env.PLASMO_PUBLIC_PREMIUM_LIFETIME;
    },
    privacy_url: function () {
        return process.env.PLASMO_PUBLIC_PRIVACY;
    },
    terms_url: function () {
        return process.env.PLASMO_PUBLIC_TERMS;
    },
    eula_url: function () {
        return process.env.PLASMO_PUBLIC_EULA;
    },
    cloud_key: function () {
        return process.env.PLASMO_PUBLIC_CLOUD_KEY;
    },
    service_api: function () {
        return process.env.PLASMO_PUBLIC_SERVICE_API;
    },
    getCurrentTab: function () {
        return new Promise(async (resolve, reject) => {
            try {
                let queryOptions = { active: true, currentWindow: true };
                let [tab] = await chrome.tabs.query(queryOptions);
                resolve(tab);
            } catch (error) {
                reject(error);
            }
        });
    },
    apiEndpoint: function (action: string) {
        return Basic.service_api() + action + '?ext=' + encodeURIComponent(Basic.cloud_key()) + '&flush=' + encodeURIComponent(Math.random());
    },
    storeMembership: function (data: any) {
        chrome.storage.local.set({ 'membership': JSON.stringify(data) }, function () {});
    },
    getMembership: function () {
        return new Promise((resolve, reject) => {
            chrome.storage.local.get('membership', function (result) {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve(result.membership != undefined ? JSON.parse(result.membership) : {});
                }
            });
        });
    },
    getMembershipType: async function(){
        let membership: any = await Basic.getMembership();
        if (Object.keys(membership).length === 0 && membership.constructor === Object) {
            return 'free';
        } else {
            return membership.type == 'premium' ? 'premium' : 'free';
        }
    },
    subscribeURL: function () {
        return Basic.apiEndpoint('subscribe');
    },
    checkMembership: async function () {
        let membership: any = await Basic.getMembership();
        if(membership.type == 'premium'){
            axios.post(Basic.apiEndpoint('check-sub'), {
                key: membership.key
            }).then(function (req_response: any) {
                let response = req_response.data;
                if(response.status == 'success'){
                    if(response.result.status == 'SUB_ACTIVE'){
                        Basic.storeMembership({
                            type: 'premium',
                            key: response.result.key,
                            expires_at: response.result.expires_at
                        });
                    } else if(response.result.status == 'SUB_EXPIRED' || response.result.status == 'SUB_NOTFOUND'){
                        Basic.storeMembership({
                            type: 'free',
                            key: '',
                            expires_at: 'never'
                        });
                    }
                } else {
                    if(response.result == 'SUB_NOTFOUND' || response.result == 'PRODUCT_NOTFOUND'){
                        Basic.storeMembership({
                            type: 'free',
                            key: '',
                            expires_at: 'never'
                        });
                    }
                }
            })
            .catch(function (error) {});
        }
    },
    activateMembership: function (email: string, callback = (status: Boolean, response: any) => {}) {
        if(email != ''){
            axios.post(Basic.apiEndpoint('verify-sub'), {
                email: email
            }).then(function (req_response: any) {
                let response = req_response.data;
                if(response.status == 'success'){
                    if(response.result.status == 'SUB_ACTIVE'){
                        Basic.storeMembership({
                            type: 'premium',
                            key: response.result.key,
                            expires_at: response.result.expires_at
                        });
                    } else if(response.result.status == 'SUB_EXPIRED'){
                        Basic.storeMembership({
                            type: 'free',
                            key: '',
                            expires_at: 'never'
                        });
                    }
                } else {
                    if(response.result == 'SUB_NOTFOUND' || response.result == 'PRODUCT_NOTFOUND'){
                        Basic.storeMembership({
                            type: 'free',
                            key: '',
                            expires_at: 'never'
                        });
                    }
                }
                callback(true, response);
            })
            .catch(function (error) {
                const errorMessage = error.response ? error.response.data.message : error.message || 'An error occurred';
                callback(false, errorMessage);
            });
        } else {
            callback(false, Basic.l("invalid_email_key"));
        }
    },
    activateLegendary: function ( callback = () => {}) {
        Basic.storeMembership({
            type: 'free',
            key: '',
            expires_at: 'never'
        });
        callback();
    }
}

export default Basic; 