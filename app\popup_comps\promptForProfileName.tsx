import React from 'react';
import { createRoot } from 'react-dom/client';

// Icons
import EditProfileName from './editProfileName';

const promptForProfileName = (defaultProfileName = '', note: string = '') => {
    return new Promise((resolve, reject) => {
        const container = document.createElement('div');
        document.body.appendChild(container);
        const root = createRoot(container);

        const handleClose = (name) => {
            root.unmount();
            if (name !== undefined) {
                resolve(name);
            } else {
                reject();
            }
        };
        root.render(
            <EditProfileName
                defaultProfileName={defaultProfileName}
                note={note}
                callback={(name) => handleClose(name)}
            />
        );
    });
};

export default promptForProfileName;