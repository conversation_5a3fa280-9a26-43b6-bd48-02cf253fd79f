import Basic from '../Basic';
import promptForProfileName from "./promptForProfileName";

const addNewProfile = async (currentProfile: any, setProfilesList: any, switchAfter: boolean = false, profileManager: any, requestProfile: any, tabSettings: any) => {
    try {
        const name = await promptForProfileName('', Basic.l('new_profile_note'));
        if (tabSettings.connected) {
            requestProfile(async (newProfile) => {
                newProfile.name = name;
                newProfile.status = false;
                newProfile.connected = false;
                await profileManager.addProfile(newProfile, (profile, latestProfilesList) => {
                    setProfilesList(latestProfilesList);
                }, switchAfter);
            });
        } else {
            let newProfile = { ...currentProfile };
            newProfile.name = name;
            newProfile.status = false;
            newProfile.connected = false;
            await profileManager.addProfile(newProfile, (profile, latestProfilesList) => {
                setProfilesList(latestProfilesList);
            }, switchAfter);
        }
    } catch (error) { }
};

export default addNewProfile;